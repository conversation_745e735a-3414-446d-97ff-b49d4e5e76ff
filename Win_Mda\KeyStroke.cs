﻿using System;
using System.IO;
using System.Reflection;
using System.Windows.Forms;
using Gma.System.MouseKeyHook;

namespace Capture
{
    public class KeyStroke
    {
        private IKeyboardMouseEvents m_GlobalHook;
        private Timer timer;
        private int _mouseStrokeCount = 0;
        private int _keyboardStrokeCount = 0;
        private int _elapsedSecond = 0;

        public void Subscribe()
        {
            try
            {
                m_GlobalHook = Hook.GlobalEvents();
                m_GlobalHook.KeyUp += M_GlobalHook_KeyUp;
                m_GlobalHook.MouseUp += M_GlobalHook_MouseUp;

                timer = new Timer();
                timer.Interval = 1000;
                timer.Enabled = true;
                timer.Tick += Timer_Tick;
                timer.Start();
            }
            catch (Exception) { }
        }

       

        public void Unsubscribe()
        {
            try
            {
                m_GlobalHook.KeyUp -= M_GlobalHook_KeyUp;
                m_GlobalHook.MouseUp -= M_GlobalHook_MouseUp;
                m_GlobalHook.Dispose();

                timer.Stop();
                timer.Tick -= Timer_Tick;
                timer.Dispose();
            }
            catch (Exception) { }
        }
        public Stroke GetStroke()
        {
            Stroke stroke = new Stroke();
            stroke.KeyStrokePerMin = _keyboardStrokeCount;
            stroke.MouseStrokePerMin = _mouseStrokeCount;

            ResetCounts();
            return stroke;
            //try
            //{
            //    // calculate stroke per minutes
            //    //double elapsedMin = Math.Ceiling(_elapsedSecond / 60d);

            //    //if (elapsedMin > 0)
            //    //{
            //    //    stroke.KeyStrokePerMin = (int)(_keyboardStrokeCount / elapsedMin);
            //    //    stroke.MouseStrokePerMin = (int)(_mouseStrokeCount / elapsedMin);
            //    //}

            //    // reset count
            //    ResetCounts();
            //}
            //catch (Exception ex) {
            //}

           // return stroke;
        }

        private void ResetCounts()
        {
            _mouseStrokeCount = 0;
            _keyboardStrokeCount = 0;
            _elapsedSecond = 0;
        }
        private void Timer_Tick(object sender, EventArgs e)
        {
            _elapsedSecond = _elapsedSecond + 1;
        }
        private void M_GlobalHook_MouseUp(object sender, MouseEventArgs e)
        {
            _mouseStrokeCount = _mouseStrokeCount + 1;
        }
        private void M_GlobalHook_KeyUp(object sender, KeyEventArgs e)
        {
            _keyboardStrokeCount = _keyboardStrokeCount + 1;
        }
    }

    public class Stroke
    {
        public int KeyStrokePerMin { get; set; }
        public int MouseStrokePerMin { get; set; }
    }
}
