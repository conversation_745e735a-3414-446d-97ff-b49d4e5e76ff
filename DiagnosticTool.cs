using System;
using System.Collections.Generic;
using LocalDataBank;
using ObjectModels;
using Utils;

namespace PCSMS.Diagnostics
{
    /// <summary>
    /// Diagnostic tool to check the current state of PCSMS desktop app
    /// Add this class to your PCSMS project and call DiagnosticTool.RunDiagnostics() from your main form
    /// </summary>
    public static class DiagnosticTool
    {
        public static void RunDiagnostics()
        {
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("PCSMS DESKTOP APP DIAGNOSTIC TOOL");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine($"Diagnostic Run Time: {DateTime.Now}");
            Console.WriteLine();

            CheckCompanyInfo();
            CheckLicenseInfo();
            CheckUserInfo();
            CheckWorkSchedule();
            CheckScreenshotValidation();
            CheckLocalDatabase();
            CheckSystemInfo();

            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("DIAGNOSTIC COMPLETE");
            Console.WriteLine("=".PadRight(60, '='));
        }

        private static void CheckCompanyInfo()
        {
            Console.WriteLine("1. COMPANY INFORMATION");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                var company = SettingsDB.GetCompany();
                if (company == null)
                {
                    Console.WriteLine("❌ ERROR: No company information found");
                    Console.WriteLine("   Solution: Enter company code in the main application");
                }
                else
                {
                    Console.WriteLine("✅ Company information found");
                    Console.WriteLine($"   Company ID: {company.Id}");
                    Console.WriteLine($"   Company Code: {company.Code}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading company info: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckLicenseInfo()
        {
            Console.WriteLine("2. LICENSE INFORMATION");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                var license = SettingsDB.GetLicense();
                if (license == null)
                {
                    Console.WriteLine("❌ ERROR: No license found");
                    Console.WriteLine("   Solution: Activate license in the main application");
                }
                else
                {
                    Console.WriteLine("✅ License found");
                    Console.WriteLine($"   License ID: {license.Id}");
                    Console.WriteLine($"   Company Code: {license.CompanyCode}");
                    Console.WriteLine($"   License Key: {license.LicenseKey ?? "TRIAL"}");
                    Console.WriteLine($"   Status: {license.Status}");
                    Console.WriteLine($"   Activated On: {license.ActivatedOn}");
                    Console.WriteLine($"   Expiry Date: {license.ExpiryDate}");
                    
                    // Check license validity
                    bool isActive = license.Status.Equals("Active", StringComparison.OrdinalIgnoreCase);
                    bool notExpired = DateTime.Now <= license.ExpiryDate;
                    
                    if (isActive && notExpired)
                    {
                        Console.WriteLine("✅ License is ACTIVE and VALID");
                    }
                    else
                    {
                        Console.WriteLine("❌ License is INACTIVE or EXPIRED");
                        if (!isActive) Console.WriteLine("   Issue: License status is not Active");
                        if (!notExpired) Console.WriteLine("   Issue: License has expired");
                        Console.WriteLine("   Solution: Reactivate or renew license");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading license info: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckUserInfo()
        {
            Console.WriteLine("3. USER INFORMATION");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                var user = SettingsDB.GetUserInfo();
                if (user == null)
                {
                    Console.WriteLine("❌ ERROR: No user logged in");
                    Console.WriteLine("   Solution: Login with your credentials in the main application");
                }
                else
                {
                    Console.WriteLine("✅ User logged in");
                    Console.WriteLine($"   User ID: {user.Id}");
                    Console.WriteLine($"   Name: {user.FirstName} {user.LastName}");
                    Console.WriteLine($"   Email: {user.Email}");
                    Console.WriteLine($"   Designation: {user.Designation}");
                    Console.WriteLine($"   Company: {user.CompanyName}");
                    Console.WriteLine($"   Status: {user.Status}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading user info: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckWorkSchedule()
        {
            Console.WriteLine("4. WORK SCHEDULE");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                var schedule = SettingsDB.GetWorkSchedule();
                if (schedule == null)
                {
                    Console.WriteLine("❌ ERROR: No work schedule found");
                    Console.WriteLine("   Solution: Work schedule should be downloaded after login");
                    Console.WriteLine("   Check server-side device configuration");
                }
                else
                {
                    Console.WriteLine("✅ Work schedule found");
                    Console.WriteLine($"   Schedule ID: {schedule.Id}");
                    Console.WriteLine($"   Device Name: {schedule.DeviceName}");
                    Console.WriteLine($"   Start Time: {schedule.StartTime.ToString("HH:mm:ss")}");
                    Console.WriteLine($"   End Time: {schedule.EndTime.ToString("HH:mm:ss")}");
                    Console.WriteLine($"   Interval: {schedule.IntervalInMinute} minutes");
                    Console.WriteLine($"   Random Interval: {schedule.IsRandom}");
                    
                    Console.WriteLine("   Working Days:");
                    if (schedule.WorkingDays != null)
                    {
                        foreach (var day in schedule.WorkingDays)
                        {
                            string status = day.Value ? "✅" : "❌";
                            Console.WriteLine($"     {status} {day.Key}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("     ❌ No working days configured");
                    }
                    
                    // Check current day and time
                    string currentDay = DateTime.Now.DayOfWeek.ToString();
                    bool isWorkingDay = schedule.WorkingDays != null && 
                                       schedule.WorkingDays.ContainsKey(currentDay) && 
                                       schedule.WorkingDays[currentDay];
                    
                    Console.WriteLine($"   Current Day: {currentDay} ({(isWorkingDay ? "Working Day" : "Non-Working Day")})");
                    
                    if (isWorkingDay)
                    {
                        var startTime = DateTime.Parse(schedule.StartTime.ToString("HH:mm:ss"));
                        var endTime = DateTime.Parse(schedule.EndTime.ToString("HH:mm:ss"));
                        var currentTime = DateTime.Now;
                        bool inWorkingHours = startTime <= currentTime && endTime >= currentTime;
                        
                        Console.WriteLine($"   Current Time: {currentTime.ToString("HH:mm:ss")} ({(inWorkingHours ? "Within Working Hours" : "Outside Working Hours")})");
                        
                        if (inWorkingHours)
                        {
                            Console.WriteLine("✅ Currently in working hours - Screenshots should be captured");
                        }
                        else
                        {
                            Console.WriteLine("⚠️  Currently outside working hours - No screenshots will be captured");
                        }
                    }
                    else
                    {
                        Console.WriteLine("⚠️  Today is not a working day - No screenshots will be captured");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading work schedule: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckScreenshotValidation()
        {
            Console.WriteLine("5. SCREENSHOT VALIDATION");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                bool validationPassed = RunValidationCheck();
                if (validationPassed)
                {
                    Console.WriteLine("✅ ALL VALIDATIONS PASSED - Screenshots should be captured");
                }
                else
                {
                    Console.WriteLine("❌ VALIDATION FAILED - Screenshots will NOT be captured");
                    Console.WriteLine("   Check the issues identified above");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception during validation: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckLocalDatabase()
        {
            Console.WriteLine("6. LOCAL DATABASE");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                var screenshotDB = new ScreenshotDB();
                var screenshots = screenshotDB.GetScreenshots();
                
                if (screenshots == null)
                {
                    Console.WriteLine("⚠️  No screenshots in local database");
                }
                else
                {
                    Console.WriteLine($"📊 Found {screenshots.Count} screenshots in local database");
                    if (screenshots.Count > 0)
                    {
                        var latest = screenshots[screenshots.Count - 1];
                        Console.WriteLine($"   Latest screenshot: {latest.CapturedOn}");
                        Console.WriteLine($"   User ID: {latest.UserId}");
                        Console.WriteLine($"   Device ID: {latest.DeviceUniqueId}");
                        Console.WriteLine($"   Keystrokes: {latest.KeyStroke}");
                        Console.WriteLine($"   Mouse Clicks: {latest.MouseClick}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading local database: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static void CheckSystemInfo()
        {
            Console.WriteLine("7. SYSTEM INFORMATION");
            Console.WriteLine("-".PadRight(40, '-'));
            
            try
            {
                Console.WriteLine($"   Device ID: {App.GetCpuId()}");
                Console.WriteLine($"   OS Version: {Environment.OSVersion}");
                Console.WriteLine($"   .NET Version: {Environment.Version}");
                Console.WriteLine($"   Machine Name: {Environment.MachineName}");
                Console.WriteLine($"   User Domain: {Environment.UserDomainName}");
                Console.WriteLine($"   User Name: {Environment.UserName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: Exception reading system info: {ex.Message}");
            }
            Console.WriteLine();
        }

        private static bool RunValidationCheck()
        {
            try
            {
                var license = SettingsDB.GetLicense();
                // check is license active or not
                if (license != null && license.Status.Equals("Active") && DateTime.Now <= license.ExpiryDate)
                {
                    // check is user signed in or not
                    if (SettingsDB.GetUserInfo() != null)
                    {
                        var schedule = SettingsDB.GetWorkSchedule();
                        if (schedule != null)
                        {
                            // check is day is working day or not
                            if (schedule.WorkingDays != null && schedule.WorkingDays.ContainsKey(DateTime.Now.DayOfWeek.ToString()) && schedule.WorkingDays[DateTime.Now.DayOfWeek.ToString()])
                            {
                                // check is now working time or not
                                if (DateTime.Parse(schedule.StartTime.ToString("HH:mm:ss")) <= DateTime.Now && DateTime.Parse(schedule.EndTime.ToString("HH:mm:ss")) >= DateTime.Now)
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception) { }
            return false;
        }
    }
}
