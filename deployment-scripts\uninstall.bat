@echo off
setlocal enabledelayedexpansion

REM PCSMS Desktop Application Uninstaller
REM Version 1.0
REM Requires Administrator privileges

echo ===============================================
echo PCSMS - PC Screen Monitoring System Uninstaller
echo ===============================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This uninstaller requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Confirm uninstallation
set /p CONFIRM="Are you sure you want to uninstall PCSMS? This will remove all application files and data. (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Uninstallation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting uninstallation...

set INSTALL_DIR=C:\Program Files\PCSMS

REM Stop and kill all PCSMS processes
echo Stopping PCSMS processes...
taskkill /F /IM "Sm_system.exe" >nul 2>&1
taskkill /F /IM "ScreenCapturingApp.exe" >nul 2>&1
taskkill /F /IM "sa_vt.exe" >nul 2>&1

REM Stop and uninstall Windows service
echo Stopping and uninstalling Windows service...
net stop "Screenshot Capturing Service" >nul 2>&1
net stop "PCSMS Screenshot Service" >nul 2>&1

if exist "%INSTALL_DIR%\ScreenshotService.exe" (
    "%INSTALL_DIR%\ScreenshotService.exe" /uninstall >nul 2>&1
)

REM Remove Windows Firewall rules
echo Removing Windows Firewall rules...
netsh advfirewall firewall delete rule name="PCSMS Main App" >nul 2>&1
netsh advfirewall firewall delete rule name="PCSMS Screen Capture" >nul 2>&1

REM Remove from Windows Programs list
echo Removing from Windows Programs list...
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /f >nul 2>&1

REM Remove desktop shortcut
echo Removing desktop shortcut...
del "%PUBLIC%\Desktop\PCSMS.lnk" >nul 2>&1
del "%USERPROFILE%\Desktop\PCSMS.lnk" >nul 2>&1

REM Remove start menu entries
echo Removing start menu entries...
rmdir /S /Q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\PCSMS" >nul 2>&1
rmdir /S /Q "%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\PCSMS" >nul 2>&1

REM Remove application data (optional - ask user)
set /p REMOVE_DATA="Do you want to remove all application data and settings? (Y/N): "
if /i "%REMOVE_DATA%"=="Y" (
    echo Removing application data...
    rmdir /S /Q "%APPDATA%\PCSMS" >nul 2>&1
    rmdir /S /Q "%LOCALAPPDATA%\PCSMS" >nul 2>&1
    rmdir /S /Q "%ALLUSERSPROFILE%\PCSMS" >nul 2>&1
)

REM Wait a moment for processes to fully terminate
timeout /t 3 /nobreak >nul

REM Remove application files
echo Removing application files...
if exist "%INSTALL_DIR%" (
    rmdir /S /Q "%INSTALL_DIR%" >nul 2>&1
    if exist "%INSTALL_DIR%" (
        echo WARNING: Some files could not be removed. They may be in use.
        echo Please restart your computer and manually delete: %INSTALL_DIR%
    )
)

REM Clean up registry entries (if any)
echo Cleaning up registry entries...
reg delete "HKEY_CURRENT_USER\SOFTWARE\PCSMS" /f >nul 2>&1
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\PCSMS" /f >nul 2>&1

REM Remove any scheduled tasks (if created)
echo Removing scheduled tasks...
schtasks /delete /tn "PCSMS*" /f >nul 2>&1

echo.
echo ===============================================
echo Uninstallation completed!
echo ===============================================
echo.
echo PCSMS has been successfully removed from your system.
echo.
echo The following items have been removed:
echo - Application files and folders
echo - Desktop and Start menu shortcuts
echo - Windows service
echo - Windows Firewall rules
echo - Registry entries
echo.

if /i "%REMOVE_DATA%"=="Y" (
    echo - Application data and settings
    echo.
)

echo If you experience any issues, please contact your system administrator.
echo.
echo Thank you for using PCSMS!
echo.
pause
