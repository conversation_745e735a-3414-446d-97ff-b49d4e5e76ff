﻿using LiteDB;
using ObjectModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace LocalDataBank
{
    public static class SettingsDB
    {
        public static CommonApplicationData commonApplicationData;
        public static readonly string dir;
        public static readonly string uidDir;
        static SettingsDB()
        {
            try
            {
                commonApplicationData = new CommonApplicationData("Microsoft.File.System", "Win", true);
                dir = Path.Combine(commonApplicationData.ApplicationFolderPath, "win_st.dll");
                uidDir = Path.Combine(commonApplicationData.ApplicationFolderPath, "sys_da.dll");
            }
            catch (Exception) { }
        }
      


        public static bool StoreCompany(Company company)
        {
            try
            {
                company.Id = 1;
               
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var licenses = db.GetCollection<Company>("company");
                    // Insert new screenshotImage (Id will be auto-incremented)
                    return licenses.Upsert(company);
                }
            }
            catch (Exception) { }
            return false;
        }
        public static Company GetCompany()
        {
            try
            {

                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var licenses = db.GetCollection<Company>("company");
                    return licenses.FindOne(x => x.Id == 1);
                }
            }
            catch (Exception) { }
            return null;
        }



        public static void StoreLicense(License license)
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var licenses = db.GetCollection<License>("licenses");
                    var company = GetCompany();
                        
                    if (company != null)
                    {
                        var found = licenses.FindOne(x => x.CompanyCode.Equals(company.Code));
                        if (found!=null)
                        {
                            licenses.Delete(found.Id);
                        }
                            
                        license.CompanyCode = company.Code;
                        licenses.Upsert(license);
                    }

                    licenses = null;
                    company = null;
                }
            }
            catch (Exception) { }
        }
        public static License GetLicense()
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var licenses = db.GetCollection<License>("licenses");
                    var company = GetCompany();
                    if(company != null)
                    {
                        return licenses.FindOne(x => x.CompanyCode.Equals(company.Code));
                    }
                    licenses = null;
                    company = null;
                }
            }
            catch (Exception) { }
            return null;
        }
        public static bool RemoveLicense(string companyCode)
        {
            try
            {
                using (var db = new LiteDatabase(dir ))
                {
                    // Get screenshot collection
                    var licenses = db.GetCollection<License>("licenses");
                    if (licenses.Count() > 0)
                    {
                        var lic = licenses.FindOne(x => x.CompanyCode.Equals(companyCode));
                        if (lic != null)
                        {
                            return licenses.Delete(lic.Id);
                        }
                        lic = null;
                    }
                    licenses = null;
                }
            }
            catch (Exception) { }
            return false;
        }
        


        public static UserInfo GetUserInfo()
        {
            try
            {
                
                    using (var db = new LiteDatabase(dir))
                    {
                        // Get screenshot collection
                        var userInfo = db.GetCollection<UserInfo>("userInfo");
                        return userInfo.FindAll().FirstOrDefault();
                    }
            }
            catch (Exception) { }
            return null;
        }
        public static void StoreUserInfo(UserInfo userInfo)
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var userInfoes = db.GetCollection<UserInfo>("userInfo");
                    userInfoes.Delete("delect * from userInfo");
                    // Insert new screenshotImage (Id will be auto-incremented)
                    userInfoes.Upsert(userInfo);
                    userInfoes = null;
                }
            }
            catch (Exception) { }
        }
        public static bool RemoveUserInfo()
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var userInfoes = db.GetCollection<UserInfo>("userInfo");

                    while (userInfoes.Count() > 0)
                    {
                        userInfoes.Delete(GetUserInfo().Id);
                    }
                    userInfoes = null;
                }
            }
            catch (Exception) { }
            return false;
        }



        public static void StoreWorkSchedule(WorkSchedule schedule)
        {
            try
            {
                RemoveWorkSchedule();
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var schedules = db.GetCollection<WorkSchedule>("workSchedule");
                    schedules.Upsert(schedule);
                    schedules = null;
                }
            }
            catch (Exception) { }
        }
        public static void UpdateWorkingSchedule(DeviceObject deviceObj)
        {
            try
            {
                var schedule = new WorkSchedule();
                schedule.Id = deviceObj.Id;
                schedule.IntervalInMinute = deviceObj.Interval;
                schedule.StartTime = DateTime.Parse(deviceObj.StartTime);
                schedule.EndTime = DateTime.Parse(deviceObj.EndTime);
                schedule.DeviceName = deviceObj.DeviceName;
                schedule.IsRandom = deviceObj.IsRandom;

                schedule.WorkingDays = new Dictionary<string, bool>(){
                { "Saturday",deviceObj.Sat.Equals("Y")},
                    { "Sunday",deviceObj.Sun.Equals("Y") },
                    { "Monday",deviceObj.Mon.Equals("Y") },
                    { "Tuesday",deviceObj.Tues.Equals("Y") },
                    { "Wednesday",deviceObj.Wed.Equals("Y") },
                    { "Thursday",deviceObj.Thurs.Equals("Y") },
                    { "Friday",deviceObj.Fri.Equals("Y") },
                };
                StoreWorkSchedule(schedule);
            }
            catch (Exception) { }
        }
        public static WorkSchedule GetWorkSchedule()
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var schedules = db.GetCollection<WorkSchedule>("workSchedule");
                    return schedules.FindOne(x=> x.Id>0);
                }
            }
            catch (Exception) { }
            return null;
        }
        public static bool RemoveWorkSchedule()
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var schedules = db.GetCollection<WorkSchedule>("workSchedule");
                    if(schedules.Count()>0)
                    {
                        foreach (var item in schedules.FindAll())
                        {
                            schedules.Delete(item.Id);
                        }
                    }
                    return true;//schedules.Delete(GetWorkSchedule().Id);
                }
            }
            catch (Exception) { }

            return false;
        }


        //================ UID================
        public static string GetDeviceID()
        {
            try
            {
                using (var db = new LiteDatabase(uidDir))
                {
                    // Get screenshot collection
                    var uid = db.GetCollection<Company>("uid");
                    if (uid.Count() <= 0)
                    {
                        var newUid = new Company
                        {
                            Id = 1,
                            Code = Guid.NewGuid().ToString()
                        };
                        uid.Upsert(newUid);
                    }

                   return uid.FindOne(x => x.Id == 1).Code;
                }
            }
            catch (Exception) { }

            return null;
        }










        public static void Reset()
        {
            try
            {
                File.Delete(dir);
                File.Delete(Path.Combine(commonApplicationData.ApplicationFolderPath, "str_dt.dll"));
            }
            catch (Exception) { }
        }
       
    }
}
