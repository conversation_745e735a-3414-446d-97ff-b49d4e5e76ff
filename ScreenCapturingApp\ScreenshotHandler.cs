﻿using Capture;
using System;
using System.Timers;
using LocalDataBank;
using Gateway;
using ObjectModels;
using Utils;

namespace ScreenCapturingApp
{
    class ScreenshotHandler
    {

        Screenshot screenshot;
        ScreenshotDB screenshotDB;
        KeyStroke keyStroke;
        int interval =60000;
        readonly Random random;
        Timer dataSenderTimer;
        Timer screencaptureTimer;
        Server server;

        public ScreenshotHandler()
        {
            random = new Random();
            screenshot = new Screenshot();
            screenshotDB = new ScreenshotDB();
            keyStroke = new KeyStroke();
            server = new Server();

            var schedule = SettingsDB.GetWorkSchedule();
            if (schedule != null)
            {
                interval = schedule.IntervalInMinute * 60000;
                schedule = null;
            }
        }


        //--------- screenshot capture ----------
        internal void Start()
        {
            try
            {
                //-------
                keyStroke.Subscribe();


                dataSenderTimer = new Timer();
                dataSenderTimer.Interval = 30000;
                dataSenderTimer.Elapsed += DataSenderTimer_Elapsed;
                dataSenderTimer.Enabled = true;
                dataSenderTimer.Start();

                screencaptureTimer = new Timer();
                screencaptureTimer.Interval = 60000;
                screencaptureTimer.Elapsed += ScreencaptureTimer_Elapsed; ;
                screencaptureTimer.Enabled = true;
                screencaptureTimer.Start();

               
            }
            catch (Exception) { }
        }
        private void ScreencaptureTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                isRandomIntervalElapsed = true;
                if (AllRequiredValidationPassed())
                {
                    var img = GenerateScreenshotImage(screenshot.FullScreenshot());
                    if(img != null)
                    {
                        screenshotDB.storeScreenshot(img);
                    }
                    img = null;
                }
            }
            catch (Exception) { }
        }
        private ScreenshotImage GenerateScreenshotImage(System.Drawing.Bitmap bitmap)
        {
            try
            {
                var stroke = keyStroke.GetStroke();
                return new ScreenshotImage
                {
                    UserId = SettingsDB.GetUserInfo().Id,
                    ScreenShot = Screenshot.ImageToBase64String(bitmap),
                    CapturedOn = DateTime.UtcNow,
                    KeyStroke = stroke.KeyStrokePerMin,
                    MouseClick = stroke.MouseStrokePerMin,
                    DeviceUniqueId = App.GetCpuId()
                    // MacAddress = NIC.GetMACAddress(),
                    // InternetConnected = NIC.IsInternetConnected()
                };
                
            }
            catch (Exception)
            {
                return null;
            }
        }
        private bool AllRequiredValidationPassed()
        {
            try
            {
                var license = SettingsDB.GetLicense();
                // check is license active or not
                if (license != null && license.Status.Equals("Active") && DateTime.Now <= license.ExpiryDate)
                {
                    // check is user signed in or not
                    if (SettingsDB.GetUserInfo() != null)
                    {
                        var schedule = SettingsDB.GetWorkSchedule();
                        if (schedule != null)
                        {
                            // check is day is working day or not
                            if (schedule.WorkingDays[DateTime.Now.DayOfWeek.ToString()])
                            {
                                // check is now working time or not
                                if (DateTime.Parse(schedule.StartTime.ToString("HH:mm:ss")) <= DateTime.Now && DateTime.Parse(schedule.EndTime.ToString("HH:mm:ss")) >= DateTime.Now)
                                {
                                    return true;
                                }
                            }
                        }
                        schedule = null;
                    }
                }
                license = null;
            }
            catch (Exception) { }
            return false;
        }






        //============== Data sender =============
        bool isUploading = false;
        bool isRandomIntervalElapsed = true;
        private void DataSenderTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                var schedule = SettingsDB.GetWorkSchedule();
                if (schedule != null)
                {
                    if (schedule.IsRandom != null && schedule.IsRandom.Equals("Y"))
                    {
                        // set new random inverval time
                        if (isRandomIntervalElapsed)
                        {
                            this.interval = random.Next(1, 5) * 60000;
                            setNewTimeInScreenCaptureTimer();
                            isRandomIntervalElapsed = false;
                        }
                    }
                    else
                    {
                        this.interval = schedule.IntervalInMinute * 60000;
                        setNewTimeInScreenCaptureTimer();
                    }
                }


                if (!isUploading)
                {

                    var screenshots = screenshotDB.GetScreenshots();
                    if (screenshots != null && screenshots.Count > 0)
                    {
                        isUploading = true;
                        server.UploadScreenshots(screenshots, result =>
                        {
                            isUploading = false;
                            if (result.IsSuccess)
                            {
                                screenshotDB.DeleteScreenshots(screenshots);
                                screenshots = null;
                            }
                        });
                    }
                }
            }
            catch (Exception)
            {
                isUploading = false;
            }
        }

        private void setNewTimeInScreenCaptureTimer()
        {
            // set new inverval time
            if (screencaptureTimer.Interval != this.interval)
            {
                screencaptureTimer.Stop();
                screencaptureTimer.Interval = interval;
                screencaptureTimer.Start();
            }
        }
    }
}
