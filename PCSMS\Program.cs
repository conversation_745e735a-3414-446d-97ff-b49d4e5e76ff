﻿using LocalDataBank;
using PCSMS.UI;
using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace PCSMS
{
    static class Program
    {
        private static HandleUIForm uiHandler;
        // message handler
        private const int RF_TESTMESSAGE = 0xA123;
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int SendMessage(IntPtr hwnd, [MarshalAs(UnmanagedType.U4)] int Msg, IntPtr wParam, IntPtr lParam);

        
        // The main entry point for the application.
        [STAThread]
        static void Main()
        {
            Process proc = Process.GetCurrentProcess();
            Process[] processes = Process.GetProcessesByName(proc.ProcessName);

            if (processes.Length > 1)
            {
                foreach (Process p in processes)
                {
                    if (p.Id != proc.Id)
                    {
                        NativeMethods.SendMessage((IntPtr)NativeMethods.HWND_BROADCAST, NativeMethods.WM_CUSTOMTEXT_SHOWME, IntPtr.Zero, IntPtr.Zero);
                        SendMessage(p.MainWindowHandle, RF_TESTMESSAGE, IntPtr.Zero, IntPtr.Zero);
                    }
                }
            }
            else
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;

                // start splashcreen
                var splashScreen = new Splashscreen();
                splashScreen.OnSplashCompleted += SplashScreen_OnSplashCompleted;
                splashScreen.OnShowUIMessageReceived += SplashScreen_OnShowUIMessageReceived;
               
                // run app
                Application.Run(splashScreen);
            }
        }

        private static System.Reflection.Assembly CurrentDomain_AssemblyResolve(object sender, ResolveEventArgs args)
        {
            try
            {
                var name = args.Name.Split(',')[0];
                if (name.Equals("Bunifu_UI_v1.52"))
                {
                    var assem = Assembly.Load(Properties.Resources.Bunifu_UI_v1_52);
                    return assem;
                }
                else if (name.Equals("RestSharp"))
                {
                    var assem = Assembly.Load(Properties.Resources.RestSharp);
                    return assem;
                }
            }
            catch (Exception){}
            return null;
        }

        internal class NativeMethods
        {
            public const int HWND_BROADCAST = 0xffff;
            public static readonly int WM_CUSTOMTEXT_SHOWME = RegisterWindowMessage("WM_CUSTOMTEXT_SHOWME");

            [DllImport("user32")]
            public static extern bool SendMessage(IntPtr hwnd, int msg, IntPtr wparam, IntPtr lparam);

            [DllImport("user32")]
            public static extern int RegisterWindowMessage(string message);
        }



        // splashcreen event
        private static void SplashScreen_OnSplashCompleted(object sender, EventArgs e)
        {
            if(uiHandler == null)
            {
                uiHandler = new HandleUIForm();
            }
        }
        private static void SplashScreen_OnShowUIMessageReceived(object sender, EventArgs e)
        {
            if(uiHandler == null)
            {
                uiHandler = new HandleUIForm();
            }
        }


        // UI Form handler calss
        class HandleUIForm:IDisposable
        {
            frmCompanyCode frmCompany;
            frmChangePassword frmChangePass;
            frmUserDashboard frmDashboard;
            frmUserAuthentication frmAuthentication;
            frmLicenseKey frmLicense;
            frmUserDetails frmUser;

            // init forms
            public HandleUIForm()
            {
                frmCompany = new frmCompanyCode();
                frmCompany.OnCloseClicked += FrmCompany_OnCloseClicked;
                frmCompany.OnCompanyCodeApplied += FrmCompany_OnCompanyCodeApplied;


                frmChangePass = new frmChangePassword();
                frmChangePass.OnCloseClicked += FrmChangePass_OnCloseClicked;
                frmChangePass.OnPasswordChanged += FrmChangePass_OnPasswordChanged;

                frmLicense = new frmLicenseKey();
                frmLicense.OnCloseClicked += FrmLicense_OnCloseClicked;
                frmLicense.OnLicenseActivated += FrmLicense_OnLicenseActivated;
                frmLicense.OnSigninClicked += FrmLicense_OnSigninClicked;
                frmLicense.OnSwitchCompanyClicked += FrmLicense_OnSwitchCompanyClicked;


                frmAuthentication = new frmUserAuthentication();
                frmAuthentication.OnCloseClicked += FrmAuthentication_OnCloseClicked;
                frmAuthentication.OnSignInSuccess += FrmAuthentication_OnSignInSuccess;
                frmAuthentication.OnSwitchCompanyClicked += FrmAuthentication_OnSwitchCompanyClicked;
                frmAuthentication.OnActivateLicenseClicked += FrmAuthentication_OnActivateLicenseClicked;

                frmDashboard = new frmUserDashboard();
                frmDashboard.OnCloseClicked += FrmDashboard_OnCloseClicked;
                frmDashboard.OnChangeLicenseClicked += FrmDashboard_OnChangeLicenseClick;
                frmDashboard.OnChangePasswordClicked += FrmDashboard_OnChangePasswordClick;
                frmDashboard.OnUserDetailsClicked += FrmDashboard_OnUserDetailsClicked; ;
                frmDashboard.OnSignOutSuccess += FrmDashboard_OnSignOutSuccess;

                frmUser = new frmUserDetails();
                frmUser.OnCloseClicked += FrmUser_OnCloseClicked;
                frmUser.OnUserInfoUpdated += FrmUser_OnUserInfoUpdated;

                ShowAppropriatForm();
            }

         



            // Change password Form
            private void FrmChangePass_OnPasswordChanged(object sender, EventArgs e)
            {
                frmDashboard.Show();
                frmDashboard.SetTabSelected(SelectedTab.UserInfo);
                frmChangePass.Hide();
            }
            private void FrmChangePass_OnCloseClicked(object sender, EventArgs e)
            {
                frmDashboard.Show();
                frmDashboard.SetTabSelected(SelectedTab.UserInfo);
                frmChangePass.Hide();
            }



            // Company code From
            private void FrmCompany_OnCompanyCodeApplied(object sender, EventArgs e)
            {
                ShowAppropriatForm();
                frmCompany.Hide();
            }
            private void FrmCompany_OnCloseClicked(object sender, EventArgs e)
            {
                if ((sender as frmCompanyCode).OpenForm == OpenForm.LoginForm)
                {
                    ShowAppropriatForm();
                    frmCompany.Hide();
                }
                else if ((sender as frmCompanyCode).OpenForm == OpenForm.LicenceKeyForm)
                {
                    frmLicense.ShowForm();
                    frmCompany.Hide();
                }
                else
                {
                    ExitApp();
                }
            }


            // Dashboard Form
            private void FrmDashboard_OnSignOutSuccess(object sender, EventArgs e)
            {
                frmAuthentication.ShowForm();
                frmDashboard.Hide();
            }
            private void FrmDashboard_OnChangePasswordClick(object sender, EventArgs e)
            {
                frmChangePass.ShowForm();
                frmDashboard.Hide();
            }
            private void FrmDashboard_OnChangeLicenseClick(object sender, EventArgs e)
            {
                frmLicense.ShowForm();
                frmDashboard.Hide();
            }
            private void FrmDashboard_OnUserDetailsClicked(object sender, EventArgs e)
            {
                frmUser.ShowForm();
                frmDashboard.Hide();
            }
            private void FrmDashboard_OnCloseClicked(object sender, EventArgs e)
            {
                ExitApp();
            }


            //Authentication Form
            private void FrmAuthentication_OnSignInSuccess(object sender, EventArgs e)
            {
                frmDashboard.Show();
                frmDashboard.SetTabSelected(SelectedTab.UserInfo);
                frmAuthentication.Hide();
            }
            private void FrmAuthentication_OnCloseClicked(object sender, EventArgs e)
            {
                ExitApp();
            }
            private void FrmAuthentication_OnSwitchCompanyClicked(object sender, EventArgs e)
            {
                frmCompany.ShowForm(OpenForm.LoginForm);
                frmAuthentication.Hide();
            }
            private void FrmAuthentication_OnActivateLicenseClicked(object sender, EventArgs e)
            {
                frmLicense.ShowForm();
                frmAuthentication.Hide();
            }



            // Licence Form
            private void FrmLicense_OnLicenseActivated(object sender, string e)
            {
                frmLicense.Hide();
                if (SettingsDB.GetUserInfo() != null)
                {
                    frmDashboard.Show();
                    frmDashboard.SetTabSelected(SelectedTab.LicenseKey);
                }
                else
                {
                    frmAuthentication.ShowForm();
                }
            }
            private void FrmLicense_OnCloseClicked(object sender, EventArgs e)
            {
                if(SettingsDB.GetUserInfo() != null)
                {
                    frmLicense.Hide();
                    frmDashboard.Show();

                }else
                {
                    ExitApp();
                }
            }
            private void FrmLicense_OnSigninClicked(object sender, EventArgs e)
            {
                frmAuthentication.ShowForm();
                frmLicense.Hide();
            }
            private void FrmLicense_OnSwitchCompanyClicked(object sender, EventArgs e)
            {
                frmCompany.ShowForm(OpenForm.LicenceKeyForm);
                frmLicense.Hide();
            }



            // User details form
            private void FrmUser_OnCloseClicked(object sender, EventArgs e)
            {
                frmDashboard.Show();
                frmUser.Hide();
            }
            private void FrmUser_OnUserInfoUpdated(object sender, EventArgs e)
            {
                frmDashboard.SetTabSelected(SelectedTab.UserInfo);
            }



            // helper method
            private void ShowAppropriatForm()
            {
                if(SettingsDB.GetCompany() == null || SettingsDB.GetCompany().Code == null)
                {
                    frmCompany.ShowForm(OpenForm.None);
                }
                else if (SettingsDB.GetLicense() == null)
                {
                    frmLicense.ShowForm();
                }
                else if (SettingsDB.GetUserInfo() == null)
                {
                    frmAuthentication.ShowForm();
                }
                else
                {
                    frmDashboard.Show();
                }
            }
            private void ExitApp()
            {
                if (MessageBox.Show("Do you really want to exit?", "Warning..", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    this.Dispose();
                    Application.Exit();
                }
            }
            public void Dispose()
            {
                frmAuthentication.Dispose();
                frmDashboard.Dispose();
                frmLicense.Dispose();

                uiHandler = null;
            }
        }

      
        //private static void SplashScreen_FormClosed(object sender, FormClosedEventArgs e)
        //{

        //    Thread thread = new Thread(showLicenseKeyFrom);
        //    thread.SetApartmentState(ApartmentState.STA);
        //    thread.Start();
        //}

        //private static void showLicenseKeyFrom()
        //{
        //    Application.Run(new frmUserDashboard());
        //}
    }
}
