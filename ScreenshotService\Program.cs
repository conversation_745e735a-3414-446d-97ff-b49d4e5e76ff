﻿using System.ServiceProcess;

namespace ScreenshotService
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main()
        {
            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[]
            {
                new CapturingService()
            };
            ServiceBase.Run(ServicesToRun);
        }
    }
}
