﻿using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Timers;

namespace Capture
{
    public class Screenshot
    {
        [DllImport("user32.dll")]
        static extern bool GetWindowRect(IntPtr handle, ref Rectangle rect);

        [DllImport("user32.dll", EntryPoint = "GetDesktopWindow")]
        static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        static extern IntPtr FindWindowEx(IntPtr parentHandle, IntPtr childAfter, string lclassName, string windowTitle);


     
        public Bitmap FullScreenshot()
        {
            try
            {
                IntPtr desktopHwnd = FindWindowEx(GetDesktopWindow(), IntPtr.Zero, "Progman", "Program Manager");
                Rectangle bounds = new Rectangle();
                GetWindowRect(desktopHwnd, ref bounds);
                using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
                {
                    using (Graphics g = Graphics.FromImage(bitmap))
                    {
                        g.CopyFromScreen(Point.Empty, Point.Empty, bounds.Size);
                    }
                    return bitmap.Clone(bounds, PixelFormat.Undefined);
                }
            }
            catch (Exception)
            {
                return null;
            }
        }
        //public static void SaveToDisk(Bitmap bmp, String filepath, String filename, ImageFormat format)
        //{
        //    try
        //    {
        //        string fullpath = filepath + "\\" + filename;
        //        bmp.Save(fullpath, format);
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        //public static MemoryStream ImageToStream(Bitmap bmp, ImageFormat format)
        //{

        //    try
        //    {
        //        using (var stream = new MemoryStream())
        //        {
        //            bmp.Save(stream, format);
        //            return stream;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}
        public static byte[] ImageToByteArray(Bitmap bmp, ImageFormat format)
        {

            try
            {
                using (var stream = new MemoryStream())
                {
                    bmp.Save(stream, format);
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public static string ImageToBase64String(Bitmap bmp)
        {
            try
            {
                return Convert.ToBase64String(ImageToByteArray(bmp,ImageFormat.Jpeg));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        
    }
}
