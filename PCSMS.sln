﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27703.2026
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScreenMonitoringApp", "PCSMS\ScreenMonitoringApp.csproj", "{75D92CD9-7A98-41A8-BACC-0253C09B503C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Object Models", "MDLS\Object Models.csproj", "{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Capture", "Win_Mda\Capture.csproj", "{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Utils", "uti_ls\Utils.csproj", "{FE88894A-82A0-4C54-8198-E6D5136B1DE5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ImageTester", "ImageTester\ImageTester.csproj", "{5CC7C1FD-6BEB-4DD2-9FAE-C070BA8B0250}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocalDataBank", "LocalDataBank\LocalDataBank.csproj", "{B73ECE85-773F-44B0-8FA3-C18E83AD9A18}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Gateway", "Gateway\Gateway.csproj", "{90CDC65C-F42B-4976-94A3-2F8EF76D33EC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppReset", "AppReset\AppReset.csproj", "{1F4D1A28-6A83-42A5-B196-7CB03B213503}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScreenshotService", "ScreenshotService\ScreenshotService.csproj", "{A1893256-A58F-4905-BA95-05311DC90A1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScreenCapturingApp", "ScreenCapturingApp\ScreenCapturingApp.csproj", "{33C88A04-B878-40CF-8A0A-669D48DBB2A9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PushNotification", "PushNotification\PushNotification.csproj", "{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{75D92CD9-7A98-41A8-BACC-0253C09B503C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{75D92CD9-7A98-41A8-BACC-0253C09B503C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{75D92CD9-7A98-41A8-BACC-0253C09B503C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{75D92CD9-7A98-41A8-BACC-0253C09B503C}.Release|Any CPU.Build.0 = Release|Any CPU
		{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE88894A-82A0-4C54-8198-E6D5136B1DE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE88894A-82A0-4C54-8198-E6D5136B1DE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE88894A-82A0-4C54-8198-E6D5136B1DE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE88894A-82A0-4C54-8198-E6D5136B1DE5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CC7C1FD-6BEB-4DD2-9FAE-C070BA8B0250}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CC7C1FD-6BEB-4DD2-9FAE-C070BA8B0250}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CC7C1FD-6BEB-4DD2-9FAE-C070BA8B0250}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CC7C1FD-6BEB-4DD2-9FAE-C070BA8B0250}.Release|Any CPU.Build.0 = Release|Any CPU
		{B73ECE85-773F-44B0-8FA3-C18E83AD9A18}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B73ECE85-773F-44B0-8FA3-C18E83AD9A18}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B73ECE85-773F-44B0-8FA3-C18E83AD9A18}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B73ECE85-773F-44B0-8FA3-C18E83AD9A18}.Release|Any CPU.Build.0 = Release|Any CPU
		{90CDC65C-F42B-4976-94A3-2F8EF76D33EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{90CDC65C-F42B-4976-94A3-2F8EF76D33EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{90CDC65C-F42B-4976-94A3-2F8EF76D33EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{90CDC65C-F42B-4976-94A3-2F8EF76D33EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F4D1A28-6A83-42A5-B196-7CB03B213503}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F4D1A28-6A83-42A5-B196-7CB03B213503}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F4D1A28-6A83-42A5-B196-7CB03B213503}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F4D1A28-6A83-42A5-B196-7CB03B213503}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1893256-A58F-4905-BA95-05311DC90A1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1893256-A58F-4905-BA95-05311DC90A1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1893256-A58F-4905-BA95-05311DC90A1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1893256-A58F-4905-BA95-05311DC90A1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{33C88A04-B878-40CF-8A0A-669D48DBB2A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33C88A04-B878-40CF-8A0A-669D48DBB2A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33C88A04-B878-40CF-8A0A-669D48DBB2A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33C88A04-B878-40CF-8A0A-669D48DBB2A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8FE6A607-ECCC-416B-ADC5-43D38F5EAE74}
	EndGlobalSection
EndGlobal
