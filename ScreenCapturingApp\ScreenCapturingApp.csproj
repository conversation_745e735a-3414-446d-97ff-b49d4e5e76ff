﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{33C88A04-B878-40CF-8A0A-669D48DBB2A9}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>ScreenCapturingApp</RootNamespace>
    <AssemblyName>sa_vt</AssemblyName>
    <TargetFrameworkVersion>v4.5.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.AspNet.SignalR.Client, Version=2.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Client.2.3.0\lib\net45\Microsoft.AspNet.SignalR.Client.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.6.0.4\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Notification.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Notification.Designer.cs">
      <DependentUpon>Notification.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScreenshotHandler.cs" />
    <EmbeddedResource Include="Notification.resx">
      <DependentUpon>Notification.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Gateway\Gateway.csproj">
      <Project>{90cdc65c-f42b-4976-94a3-2f8ef76d33ec}</Project>
      <Name>Gateway</Name>
    </ProjectReference>
    <ProjectReference Include="..\LocalDataBank\LocalDataBank.csproj">
      <Project>{b73ece85-773f-44b0-8fa3-c18e83ad9a18}</Project>
      <Name>LocalDataBank</Name>
    </ProjectReference>
    <ProjectReference Include="..\MDLS\Object Models.csproj">
      <Project>{e295c7f1-23e2-413d-bf80-ee7db4f526d6}</Project>
      <Name>Object Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\PushNotification\PushNotification.csproj">
      <Project>{11342F9F-5CD5-4041-9399-D8BA6A7B9ABA}</Project>
      <Name>PushNotification</Name>
    </ProjectReference>
    <ProjectReference Include="..\uti_ls\Utils.csproj">
      <Project>{fe88894a-82a0-4c54-8198-e6d5136b1de5}</Project>
      <Name>Utils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Win_Mda\Capture.csproj">
      <Project>{bb0fbea3-e434-4699-83e1-4ce38d51b3bd}</Project>
      <Name>Capture</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RestSharp.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Gma.System.MouseKeyHook.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Microsoft.AspNet.SignalR.Client.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Newtonsoft.Json.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>