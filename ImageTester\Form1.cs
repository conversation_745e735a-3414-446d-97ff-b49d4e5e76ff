﻿using ObjectModels;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Management;
using System.Windows.Forms;

namespace ImageTester
{
    public partial class Form1 : Form
    {
        List<ScreenshotImage> screenshotImages;
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            LoadScreenshot();
        }

        private void LoadScreenshot()
        {
            listBox1.Items.Clear();
            LocalDataBank.ScreenshotDB db = new LocalDataBank.ScreenshotDB();
            screenshotImages = db.GetScreenshots();
            if (screenshotImages != null && screenshotImages.Count() > 0)
            {
                foreach (var item in screenshotImages)
                {
                    listBox1.Items.Add(item.Id + " => " + item.CapturedOn.ToShortDateString() + " " + item.CapturedOn.ToLongTimeString());

                }
                SetImageToViewer(0);
            }
        }

        private void listBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(listBox1.SelectedIndex>=0)
                SetImageToViewer(listBox1.SelectedIndex);
        }
        private void button1_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog();
            if(folderBrowserDialog.ShowDialog()== DialogResult.OK && listBox1.SelectedIndex >= 0)
            {
               if(folderBrowserDialog.SelectedPath != null)
                {
                    SaveImageToDisk(folderBrowserDialog.SelectedPath, listBox1.SelectedIndex);
                    MessageBox.Show("Image successfully saved.");
                }
            }
        }
        private void button2_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog();
            if (folderBrowserDialog.ShowDialog() == DialogResult.OK && screenshotImages.Count > 0)
            {
                progressBar1.Visible = true;
                for (int i=0; i < screenshotImages.Count; i++)
                {
                    SaveImageToDisk(folderBrowserDialog.SelectedPath, i);
                    progressBar1.Value = (100 * (i+1)) / screenshotImages.Count;
                }
                MessageBox.Show(screenshotImages.Count + " Image successfully saved.");
            }
        }


        private void SetImageToViewer(int selectedIndex)
        {
            progressBar1.Visible = false;
            progressBar1.Value = 0;

            var capturedImage = screenshotImages[selectedIndex];
            lblTime.Text = capturedImage.CapturedOn.ToLongDateString() + " " + capturedImage.CapturedOn.ToLongTimeString();
           // lblMacAddress.Text = capturedImage.MacAddress;
            lblKeyStroke.Text = capturedImage.KeyStroke.ToString() + "/min";
            lblMouseStroke.Text = capturedImage.MouseClick.ToString() + "/min";
            byte[] byteBuffer = Convert.FromBase64String(capturedImage.ScreenShot);
            MemoryStream memoryStream = new MemoryStream(byteBuffer);
            pictureBox1.Image = Image.FromStream(memoryStream);
        }
        private void SaveImageToDisk(string path,int selectedIndex)
        {
            var capturedImage = screenshotImages[selectedIndex];

            Bitmap bmpReturn = null;
            byte[] byteBuffer = Convert.FromBase64String(capturedImage.ScreenShot);
            MemoryStream memoryStream = new MemoryStream(byteBuffer);

            bmpReturn = (Bitmap)Bitmap.FromStream(memoryStream);

            bmpReturn.Save(path + "\\" + capturedImage.Id.ToString() + ".jpg", ImageFormat.Jpeg);
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {

            Security.UniqueIdGenerator.Value();


            LoadScreenshot();

            PushNotification.PushNotification.ShowNotification("PC Screen Monitoring System", "Capture schedule for this pc has been changed.");
        }
    }
}
