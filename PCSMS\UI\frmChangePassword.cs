﻿using Gateway;
using LocalDataBank;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Utils;

namespace PCSMS.UI
{
    public partial class frmChangePassword : Form
    {
        public event EventHandler OnCloseClicked;
        public event EventHandler OnPasswordChanged;
        bool isWaiting = false;

        #region DropShadow
        //===================================*******============================

        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmChangePassword()
        {
            InitializeComponent();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
        public void ShowForm()
        {
            base.Show();
            ResetErrorMessage();
            tbCurrentPass.Text = null;
            tbNewPass.Text = null;
            tbConfirmPass.Text = null;
        }


        private void btnClose_Click(object sender, EventArgs e)
        {
            OnCloseClicked?.Invoke(this, null);
        }
        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!isWaiting)
            {
                ResetErrorMessage();

                if (String.IsNullOrEmpty(tbCurrentPass.Text.Trim()))
                {
                    tbCurrentPass.Focus();
                    erCurrentPassword.Text = "Invalid current password";
                }
                else if (tbCurrentPass.Text.Trim().Length < 6)
                {
                    erCurrentPassword.Text = "Minimum 6 character required";
                }
                else if (String.IsNullOrEmpty(tbNewPass.Text.Trim()))
                {
                    tbNewPass.Focus();
                    erNewPassword.Text = "Invalid new password";
                }
                else if (tbNewPass.Text.Trim().Length < 6)
                {
                    erNewPassword.Text = "Minimum 6 character required";
                }
                else if (String.IsNullOrEmpty(tbConfirmPass.Text.Trim()))
                {
                    tbConfirmPass.Focus();
                    erConfirmPassword.Text = "Invalid confirm password";
                }
                else if (!tbConfirmPass.Text.Trim().Equals(tbNewPass.Text.Trim()))
                {
                    tbConfirmPass.Focus();
                    erConfirmPassword.Text = "Password not mached with new password";
                }
                else
                {
                    //validation passed
                    // server request
                    DoChangePassword(tbCurrentPass.Text.Trim(), tbConfirmPass.Text.Trim());
                }
            }
        }
        private void DoChangePassword(string currentPass, string newPass)
        {
            try
            {
                pbLoading.Visible = true;
                isWaiting = true;

                var server = new Server();
                server.ChangePassword(SettingsDB.GetUserInfo().Id,currentPass,newPass,App.GetCpuId(),SettingsDB.GetCompany().Code, result =>
                {
                    Invoke(new Action(() =>
                    {
                        pbLoading.Visible = false;
                        isWaiting = false;
                        if (result.IsSuccess)
                        {
                            MessageBox.Show(result.SuccessMessage, "Success..");
                            OnPasswordChanged?.Invoke(this, null);
                        }
                        else
                        {
                            MessageBox.Show(result.ErrorMessage, "Warning..");
                        }
                    }));
                });
            }
            catch (Exception)
            {
                pbLoading.Visible = false;
                isWaiting = false;
            }
        }

        private void ResetErrorMessage()
        {
            erNewPassword.Text = null;
            erCurrentPassword.Text = null;
            erConfirmPassword.Text = null;
        }

        private void tbCurrentPass_Paint(object sender, PaintEventArgs e)
        {
            tbCurrentPass.isPassword = true;
        }

        private void tbNewPass_Paint(object sender, PaintEventArgs e)
        {
            tbNewPass.isPassword = true;
        }

        private void tbConfirmPass_Paint(object sender, PaintEventArgs e)
        {
            tbConfirmPass.isPassword = true;
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
