﻿using System;
using System.Drawing;
using System.Windows.Forms;
using LocalDataBank;
using ObjectModels;
using PCSMS.UI;
using static PCSMS.Program;

namespace PCSMS
{
    public partial class Splashscreen : Form
    {
        Timer t;
        int screenStatus;

        public event EventHandler OnSplashCompleted;
        public event EventHandler OnShowUIMessageReceived;


      
        public Splashscreen()
        {
            InitializeComponent();
            t = new Timer();
            screenStatus = 0;
        }

        #region DropShadow
        //===================================*******============================
       
        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;


            // handle clck
            if (m.Msg == NativeMethods.WM_CUSTOMTEXT_SHOWME)
            {
                OnShowUIMessageReceived?.Invoke(this, null);
            }
            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }


    private void Splashscreen_Load(object sender, EventArgs e)
        {
            StartSplashAnimation();
        }
      
        
        private void StartSplashAnimation()
        {
            Opacity = 0;
            t.Enabled = true;
            t.Interval = 1;
            t.Tick += T_Tick;
            t.Start();
        }
        // Screen animation
        private void T_Tick(object sender, EventArgs e)
        {
            switch (screenStatus)
            {
                case 0:
                    {
                        if (Opacity < 1)
                        {
                            Opacity = Opacity + 0.05;
                        }
                        else
                        {
                            screenStatus = 1;
                            System.Threading.Thread.Sleep(1000);
                        }

                        break;
                    }
                case 1:
                    {
                        if (bunifuProgressBar1.Value < 200)
                        {
                            bunifuProgressBar1.Value = bunifuProgressBar1.Value + 1;
                        }
                        else
                        {
                            lblStatusText.Text = "Service started.";
                            screenStatus = 2;
                            System.Threading.Thread.Sleep(2000);
                        }
                        break;
                    }
                case 2:
                    {
                        if (this.Opacity > 0)
                        {
                            this.Opacity = Opacity - 0.05;

                        }
                        else
                        {
                            t.Stop();
                            Hide();
                            this.WindowState = FormWindowState.Minimized;
                            screenStatus = 0;

                            OnSplashCompleted?.Invoke(this,null);
                        }
                        break;
                    }
            }
        }

        
        private void Splashscreen_FormClosing(object sender, FormClosingEventArgs e)
        {
            t.Stop();
            t.Tick -= T_Tick;
            t.Dispose();
        }
        
    }

}
