﻿using Gateway;
using LocalDataBank;
using ObjectModels;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Utils;

namespace PCSMS.UI
{
    public partial class frmUserAuthentication : Form
    {

        public event EventHandler OnCloseClicked;
        public event EventHandler OnSignInSuccess;
        public event EventHandler OnSwitchCompanyClicked;
        public event EventHandler OnActivateLicenseClicked;

        bool isWorking = false;

        #region DropShadow
        //===================================*******============================

        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmUserAuthentication()
        {
            InitializeComponent();
            ResetErrorMessage();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
      


        public void ShowForm()
        {
            base.Show();
            tbUserId.Text = null;
            tbPassword.Text = null;
            ResetErrorMessage();
            tbUserId.Focus();
            pbLoading.Visible = false;

            //if(SettingsDB.GetLicense() == null)
           // {
                lblActivateLicense.Visible = true;
           // }else
           // {
            //    lblActivateLicense.Visible = false;
           //}
        }
        private void btnClose_Click(object sender, EventArgs e)
        {
            OnCloseClicked?.Invoke(this, null);
        }
        private void btnSignIn_Click(object sender, EventArgs e)
        {
           if(!isWorking)
            {
                ResetErrorMessage();
                lblForgotPassword.Focus();
                string emailRegex = @"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$";

                if (!String.IsNullOrEmpty(tbUserId.Text.Trim()) && Regex.IsMatch(tbUserId.Text.Trim(), emailRegex))
                {
                    if (!String.IsNullOrEmpty(tbPassword.Text.Trim()))
                    {
                        if (tbPassword.Text.Length >= 6)
                        {
                            // make server request
                            DoUserLogin(tbUserId.Text.Trim(), tbPassword.Text.Trim());
                        }
                        else
                        {
                            erPassword.Text = "Minimum 6 character required";
                        }
                    }
                    else
                    {
                        tbPassword.Focus();
                        erPassword.Text = "Invalid password";
                    }
                }
                else
                {
                    tbUserId.Focus();
                    erUserId.Text = "Invalid userid";
                }

            }
        }
        private void btnSwitchToCompanyCode_Click(object sender, EventArgs e)
        {
            if(!isWorking)
            {
                OnSwitchCompanyClicked?.Invoke(this, null);
            }
        }
        private void btnActivateLicense_Click(object sender, EventArgs e)
        {
            if(!isWorking)
                OnActivateLicenseClicked?.Invoke(this, null);
        }


        private void DoUserLogin(string userId, string password)
        {
                pbLoading.Visible = true;
                isWorking = true;

                // server request
                var server = new Server();
                server.UserLogin(userId, password, App.GetCpuId(), SettingsDB.GetCompany().Code, result => {
                    Invoke(new Action(() => {
                        pbLoading.Visible = false;
                        isWorking = false;
                        if(result.IsSuccess)
                        {
                            SettingsDB.StoreUserInfo(result.Content.userObj);
                            SettingsDB.UpdateWorkingSchedule(result.Content.deviceObj);
                            
                            var license = result.Content.licenseObj;
                            license.CompanyCode = SettingsDB.GetCompany().Code;
                            SettingsDB.StoreLicense(license);

                            pbLoading.Visible = false;
                            OnSignInSuccess?.Invoke(this, null);
                        }
                        else
                        {
                            MessageBox.Show(result.ErrorMessage, "Warning..");
                        }
                    }));
                });
            lblForgotPassword.Focus();
        }
      

        private void ResetErrorMessage()
        {
            erUserId.Text = null;
            erPassword.Text = null;
        }



        private void lblForgotPassword_Click(object sender, EventArgs e)
        {
           if(!isWorking)
            (new frmResetPassword()).ShowDialog();
        }
        private void lblForgotPassword_MouseDown(object sender, MouseEventArgs e)
        {
            lblForgotPassword.ForeColor = Color.FromArgb(80, 133, 125);
        }
        private void lblForgotPassword_MouseUp(object sender, MouseEventArgs e)
        {
            lblForgotPassword.ForeColor = Color.FromArgb(115, 201, 188);
        }
       
        private void tbPassword_Paint(object sender, PaintEventArgs e)
        {
            tbPassword.isPassword = true;
        }
        private void lblActivateLicense_MouseDown(object sender, MouseEventArgs e)
        {
            lblActivateLicense.ForeColor = Color.FromArgb(80, 133, 125);
        }
        private void lblActivateLicense_MouseUp(object sender, MouseEventArgs e)
        {
            lblActivateLicense.ForeColor = Color.FromArgb(115, 201, 188);
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
