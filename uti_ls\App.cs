﻿using LocalDataBank;
using Microsoft.Win32;
using System;
using System.Management;
using System.Threading.Tasks;

namespace Utils
{
    public class App
    {
        private static string _cupId=string.Empty;
      
        public static string GetCpuId()
        {
            if (string.IsNullOrEmpty(_cupId))
            {
                try
                {
                    _cupId = SettingsDB.GetDeviceID();

                    if (string.IsNullOrEmpty(_cupId))
                    {
                        _cupId = UniqueIdGenerator.Value();
                    }
                }
                catch (Exception) { }
            }
            return _cupId;
        }
        public static string GetComputerName()
        {
            string name = "No name found";
            try
            {
                name = System.Environment.MachineName;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            return name;
        }


      //=========================================
    }
}



