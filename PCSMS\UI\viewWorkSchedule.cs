﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using LocalDataBank;

namespace PCSMS.UI
{
    public partial class viewWorkSchedule : UserControl
    {
        public viewWorkSchedule()
        {
            InitializeComponent();
        }

        public void BringToFront()
        {
            base.BringToFront();
            RefreshData();
        }

        public void RefreshData()
        {
            var schedule = SettingsDB.GetWorkSchedule();

            if (schedule != null)
            {
                lblStartTime.Text = schedule.StartTime.ToString("hh:mm tt");
                lblEndTime.Text = schedule.EndTime.ToString("hh:mm tt");

                ckSat.Checked = schedule.WorkingDays["Saturday"];
                ckSun.Checked = schedule.WorkingDays["Sunday"];
                ckMon.Checked = schedule.WorkingDays["Monday"];
                ckTue.Checked = schedule.WorkingDays["Tuesday"];
                ckWed.Checked = schedule.WorkingDays["Wednesday"];
                ckThi.Checked = schedule.WorkingDays["Thursday"];
                ckFri.Checked = schedule.WorkingDays["Friday"];
            }
            else
            {
                lblStartTime.Text = "--:-- --";
                lblEndTime.Text = "--:-- --";

                ckSat.Checked = false;
                ckSun.Checked = false;
                ckMon.Checked = false;
                ckTue.Checked = false;
                ckWed.Checked = false;
                ckThi.Checked = false;
                ckFri.Checked = false;
            }
        }
    }
}
