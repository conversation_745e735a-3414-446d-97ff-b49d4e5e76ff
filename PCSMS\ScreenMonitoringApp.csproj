﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{75D92CD9-7A98-41A8-BACC-0253C09B503C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>PCSMS</RootNamespace>
    <AssemblyName>Sm_system</AssemblyName>
    <TargetFrameworkVersion>v4.5.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>ExtendedCorrectnessRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>PCSMS_Icon.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Bunifu_UI_v1.52">
      <HintPath>..\..\..\..\..\..\Program Files (x86)\Microsoft Visual Studio\Shared\Bunifu_UI_v1.52.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LiteDB, Version=4.1.4.0, Culture=neutral, PublicKeyToken=4ee40123013c9f27, processorArchitecture=MSIL">
      <HintPath>..\packages\LiteDB.4.1.4\lib\net40\LiteDB.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="UI\frmChangeComputerName.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmChangeComputerName.Designer.cs">
      <DependentUpon>frmChangeComputerName.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmChangePassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmChangePassword.Designer.cs">
      <DependentUpon>frmChangePassword.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmCompanyCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmCompanyCode.Designer.cs">
      <DependentUpon>frmCompanyCode.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmLicenseKey.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmLicenseKey.Designer.cs">
      <DependentUpon>frmLicenseKey.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmUpdateUserDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmUpdateUserDetails.Designer.cs">
      <DependentUpon>frmUpdateUserDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmUserDashboard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmUserDashboard.Designer.cs">
      <DependentUpon>frmUserDashboard.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmUserDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmUserDetails.Designer.cs">
      <DependentUpon>frmUserDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\MaterialTextBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\MaterialTextBox.designer.cs">
      <DependentUpon>MaterialTextBox.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Splashscreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Splashscreen.Designer.cs">
      <DependentUpon>Splashscreen.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UI\frmResetPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmResetPassword.Designer.cs">
      <DependentUpon>frmResetPassword.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\frmUserAuthentication.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\frmUserAuthentication.Designer.cs">
      <DependentUpon>frmUserAuthentication.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\viewLisenceKey.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\viewLisenceKey.Designer.cs">
      <DependentUpon>viewLisenceKey.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\viewUserInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\viewUserInfo.Designer.cs">
      <DependentUpon>viewUserInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\viewWorkSchedule.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\viewWorkSchedule.Designer.cs">
      <DependentUpon>viewWorkSchedule.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="UI\frmChangeComputerName.resx">
      <DependentUpon>frmChangeComputerName.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmChangePassword.resx">
      <DependentUpon>frmChangePassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmCompanyCode.resx">
      <DependentUpon>frmCompanyCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmLicenseKey.resx">
      <DependentUpon>frmLicenseKey.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmUpdateUserDetails.resx">
      <DependentUpon>frmUpdateUserDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmUserDashboard.resx">
      <DependentUpon>frmUserDashboard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmUserDetails.resx">
      <DependentUpon>frmUserDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\MaterialTextBox.resx">
      <DependentUpon>MaterialTextBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Splashscreen.resx">
      <DependentUpon>Splashscreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="UI\frmResetPassword.resx">
      <DependentUpon>frmResetPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\frmUserAuthentication.resx">
      <DependentUpon>frmUserAuthentication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\viewLisenceKey.resx">
      <DependentUpon>viewLisenceKey.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\viewUserInfo.resx">
      <DependentUpon>viewUserInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\viewWorkSchedule.resx">
      <DependentUpon>viewWorkSchedule.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\time-passing.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AppReset\AppReset.csproj">
      <Project>{1f4d1a28-6a83-42a5-b196-7cb03b213503}</Project>
      <Name>AppReset</Name>
    </ProjectReference>
    <ProjectReference Include="..\Gateway\Gateway.csproj">
      <Project>{90cdc65c-f42b-4976-94a3-2f8ef76d33ec}</Project>
      <Name>Gateway</Name>
    </ProjectReference>
    <ProjectReference Include="..\LocalDataBank\LocalDataBank.csproj">
      <Project>{b73ece85-773f-44b0-8fa3-c18e83ad9a18}</Project>
      <Name>LocalDataBank</Name>
    </ProjectReference>
    <ProjectReference Include="..\MDLS\Object Models.csproj">
      <Project>{E295C7F1-23E2-413D-BF80-EE7DB4F526D6}</Project>
      <Name>Object Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\ScreenCapturingApp\ScreenCapturingApp.csproj">
      <Project>{33c88a04-b878-40cf-8a0a-669d48dbb2a9}</Project>
      <Name>ScreenCapturingApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\ScreenshotService\ScreenshotService.csproj">
      <Project>{a1893256-a58f-4905-ba95-05311dc90a1e}</Project>
      <Name>ScreenshotService</Name>
    </ProjectReference>
    <ProjectReference Include="..\uti_ls\Utils.csproj">
      <Project>{FE88894A-82A0-4C54-8198-E6D5136B1DE5}</Project>
      <Name>Utils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Win_Mda\Capture.csproj">
      <Project>{BB0FBEA3-E434-4699-83E1-4CE38D51B3BD}</Project>
      <Name>Capture</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Group 27.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\loading_img.gif" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="PCSMS_Icon.ico" />
    <None Include="Resources\RestSharp.dll" />
    <None Include="Resources\Bunifu_UI_v1.52.dll" />
    <None Include="Resources\loading_trans.gif" />
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="Bunifu_UI_v1.52">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>False</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>