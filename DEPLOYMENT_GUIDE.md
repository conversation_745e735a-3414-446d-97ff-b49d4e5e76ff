# PCSMS Desktop Application - Complete Deployment Guide

## Overview

This guide will help you create a single installable package for your PCSMS desktop application using Visual Studio 2022. The application consists of multiple components that need to be bundled together.

## Application Components

Your PCSMS solution contains these executables:
- **`Sm_system.exe`** - Main UI application (login/configuration)
- **`ScreenCapturingApp.exe`** - Background screenshot capture
- **`ScreenshotService.exe`** - Windows service for monitoring
- **`AppReset.exe`** - Application reset utility

## Method 1: ClickOnce Deployment (Recommended for Easy Distribution)

### Step 1: Prepare Release Build

1. **Open Visual Studio 2022**
2. **Load your PCSMS.sln solution**
3. **Set to Release Configuration:**
   ```
   Build → Configuration Manager → Active solution configuration → Release
   ```
4. **Build the entire solution:**
   ```
   Build → Build Solution (Ctrl+Shift+B)
   ```

### Step 2: Configure ClickOnce Publishing

1. **Right-click** on `ScreenMonitoringApp` project (main project)
2. **Select "Publish..."**
3. **Choose Target:**
   - Select **Folder**
   - Set path: `C:\PCSMS-Deployment\`
4. **Configure Settings:**
   - **How will users install**: From a CD-ROM or DVD-ROM
   - **Where will the application check for updates**: The application will not check for updates

### Step 3: Customize Deployment

1. **In Publish Properties:**
   - **Application Name**: PCSMS - PC Screen Monitoring System
   - **Publisher**: Your Company Name
   - **Version**: *******
   - **Description**: Employee monitoring and screen capture application

2. **Prerequisites:**
   - Check **.NET Framework 4.5.1** (or higher)
   - Check **Windows Installer 4.5**

3. **Application Files:**
   - Click **Application Files** button
   - Ensure all required DLLs are included
   - Set **Publish Status** to **Include** for all components

### Step 4: Publish the Application

1. **Click "Publish Now"**
2. **Wait for publishing to complete**
3. **Find your deployment files** in `C:\PCSMS-Deployment\`

## Method 2: Manual Deployment Package (Full Control)

### Step 1: Build All Projects

1. **Build in Release mode:**
   ```
   Build → Batch Build → Select All Release configurations → Build
   ```

2. **Collect all executables** from these locations:
   ```
   PCSMS\bin\Release\Sm_system.exe
   ScreenCapturingApp\bin\Release\ScreenCapturingApp.exe
   ScreenshotService\bin\Release\ScreenshotService.exe
   AppReset\bin\Release\AppReset.exe
   ```

### Step 2: Create Deployment Folder Structure

Create this folder structure:
```
PCSMS-Installer\
├── App\
│   ├── Sm_system.exe (Main application)
│   ├── ScreenCapturingApp.exe
│   ├── ScreenshotService.exe
│   ├── AppReset.exe
│   ├── All DLL files
│   └── Config files
├── Install\
│   ├── setup.bat
│   └── uninstall.bat
└── README.txt
```

### Step 3: Copy Required Files

Copy these files to the `App\` folder:
- All `.exe` files from Release builds
- All `.dll` files (dependencies)
- All `.config` files
- Any resource files

### Step 4: Create Installation Scripts

**Create `Install\setup.bat`:**
```batch
@echo off
echo Installing PCSMS Desktop Application...

REM Create application directory
mkdir "C:\Program Files\PCSMS" 2>nul

REM Copy application files
xcopy /E /I /Y "App\*" "C:\Program Files\PCSMS\"

REM Install Windows Service
"C:\Program Files\PCSMS\ScreenshotService.exe" /install

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\PCSMS.lnk'); $Shortcut.TargetPath = 'C:\Program Files\PCSMS\Sm_system.exe'; $Shortcut.Save()"

REM Create start menu entry
mkdir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\PCSMS" 2>nul
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\Microsoft\Windows\Start Menu\Programs\PCSMS\PCSMS.lnk'); $Shortcut.TargetPath = 'C:\Program Files\PCSMS\Sm_system.exe'; $Shortcut.Save()"

echo Installation completed successfully!
pause
```

**Create `Install\uninstall.bat`:**
```batch
@echo off
echo Uninstalling PCSMS Desktop Application...

REM Stop and uninstall service
net stop "Screenshot Capturing Service" 2>nul
"C:\Program Files\PCSMS\ScreenshotService.exe" /uninstall 2>nul

REM Remove application files
rmdir /S /Q "C:\Program Files\PCSMS" 2>nul

REM Remove shortcuts
del "%USERPROFILE%\Desktop\PCSMS.lnk" 2>nul
rmdir /S /Q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\PCSMS" 2>nul

echo Uninstallation completed!
pause
```

## Method 3: Professional Installer (Advanced)

### Using WiX Toolset

1. **Install WiX Toolset** from https://wixtoolset.org/
2. **Add WiX Setup Project** to your solution
3. **Configure installer properties**
4. **Build MSI package**

### Using InstallShield or Advanced Installer

1. **Download InstallShield** or **Advanced Installer**
2. **Create new project**
3. **Add all application files**
4. **Configure installation logic**
5. **Build installer package**

## Configuration for Client Deployment

### Step 1: Update Configuration Files

**Modify `App.config` in main project:**
```xml
<configuration>
  <appSettings>
    <!-- Update server URL for client environment -->
    <add key="ServerUrl" value="https://client-server.com/api/" />
    <add key="CompanyCode" value="" />
  </appSettings>
</configuration>
```

### Step 2: Create Client-Specific Build

1. **Create separate configuration** for each client
2. **Use build configurations** to switch settings
3. **Automate deployment** with different configs

## Testing Your Deployment

### Pre-Deployment Checklist

- [ ] All projects build successfully in Release mode
- [ ] All dependencies are included
- [ ] Configuration files are correct
- [ ] Application runs on clean test machine
- [ ] Windows service installs correctly
- [ ] All features work as expected

### Test Installation Process

1. **Test on clean Windows VM**
2. **Run installer as administrator**
3. **Verify all components are installed**
4. **Test application functionality**
5. **Test uninstallation process**

## Distribution Options

### Option 1: Direct File Sharing
- Zip the deployment folder
- Share via email/cloud storage
- Include installation instructions

### Option 2: Download Portal
- Upload to company website
- Provide download link to clients
- Include version information

### Option 3: USB/CD Distribution
- Burn to CD/DVD
- Copy to USB drives
- Include autorun.inf for automatic launch

## Troubleshooting Common Issues

### Issue 1: Missing Dependencies
**Solution:** Use Dependency Walker or similar tool to identify missing DLLs

### Issue 2: Permission Issues
**Solution:** Run installer as administrator, check UAC settings

### Issue 3: Service Installation Fails
**Solution:** Ensure InstallUtil.exe is available, run with admin rights

### Issue 4: Application Won't Start
**Solution:** Check .NET Framework version, verify all files copied

## Maintenance and Updates

### Version Management
- Update assembly versions before each release
- Maintain changelog for client communication
- Test updates on existing installations

### Update Distribution
- Create update packages with only changed files
- Implement automatic update checking
- Provide manual update instructions

## Security Considerations

### Code Signing
- Sign executables with valid certificate
- Prevents security warnings during installation
- Builds trust with clients

### Antivirus Compatibility
- Test with major antivirus software
- Submit to antivirus vendors if flagged
- Document any false positive issues

## Client Delivery Package

### Final Package Should Include:
```
PCSMS-v1.0-Client-Package\
├── Installer\
│   ├── setup.exe (or setup.msi)
│   └── Prerequisites\
├── Documentation\
│   ├── Installation-Guide.pdf
│   ├── User-Manual.pdf
│   └── Troubleshooting-Guide.pdf
├── Support\
│   ├── Diagnostic-Tool.exe
│   └── Log-Collector.bat
└── README.txt
```

### README.txt Template:
```
PCSMS Desktop Application v1.0
==============================

SYSTEM REQUIREMENTS:
- Windows 7/8/10/11
- .NET Framework 4.5.1 or higher
- Administrator privileges for installation
- Internet connection for server communication

INSTALLATION:
1. Run setup.exe as Administrator
2. Follow installation wizard
3. Enter company code when prompted
4. Activate license (trial or premium)
5. Login with employee credentials

SUPPORT:
- Email: <EMAIL>
- Phone: +1-XXX-XXX-XXXX
- Documentation: See Documentation folder

COPYRIGHT:
© 2025 Your Company Name. All rights reserved.
```

This comprehensive guide will help you create a professional deployment package for your PCSMS desktop application that clients can easily install and use.
