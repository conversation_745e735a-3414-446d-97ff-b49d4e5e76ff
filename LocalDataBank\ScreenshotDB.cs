﻿using LiteDB;
using ObjectModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace LocalDataBank
{
    public class ScreenshotDB
    {
        string dir;
        public ScreenshotDB()
        {
            dir = Path.Combine(SettingsDB.commonApplicationData.ApplicationFolderPath, "str_dt.dll");
        }


        public bool storeScreenshot(ScreenshotImage screenshot)
        {
            try
            {
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var screenshotes = db.GetCollection<ScreenshotImage>("screenshotes");
                    // Insert new screenshotImage (Id will be auto-incremented)
                    var res = screenshotes.Insert(screenshot);
                    return res > 0;
                }

            }
            catch (Exception)
            {
                return false;
            }
        }

        public List<ScreenshotImage> GetScreenshots()
        {
            try
            {
                
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var screenshotes = db.GetCollection<ScreenshotImage>("screenshotes");
                    return screenshotes.FindAll().ToList();
                }
            }
            catch (Exception) { }
            return null;
        }

        public void DeleteScreenshots(List<ScreenshotImage> screenshots)
        {
            try
            {
                
                using (var db = new LiteDatabase(dir))
                {
                    // Get screenshot collection
                    var screenshotes = db.GetCollection<ScreenshotImage>("screenshotes");
                    foreach (var item in screenshots)
                    {
                        screenshotes.Delete(item.Id);
                    }
                    db.Shrink();
                }

            }
            catch (Exception) { }
        }
    }
}
