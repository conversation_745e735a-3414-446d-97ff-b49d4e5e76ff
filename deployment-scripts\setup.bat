@echo off
setlocal enabledelayedexpansion

REM PCSMS Desktop Application Installer
REM Version 1.0
REM Requires Administrator privileges

echo ===============================================
echo PCSMS - PC Screen Monitoring System Installer
echo ===============================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This installer requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Checking system requirements...

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows Version: %VERSION%

REM Check .NET Framework
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: .NET Framework 4.5.1 or higher is required.
    echo Please install .NET Framework and try again.
    echo.
    pause
    exit /b 1
)
echo .NET Framework: OK

echo.
echo Starting installation...

REM Create application directory
set INSTALL_DIR=C:\Program Files\PCSMS
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Stop existing services if running
echo Stopping existing services...
net stop "PCSMS Screenshot Service" >nul 2>&1
net stop "Screenshot Capturing Service" >nul 2>&1

REM Kill existing processes
echo Stopping existing processes...
taskkill /F /IM "Sm_system.exe" >nul 2>&1
taskkill /F /IM "ScreenCapturingApp.exe" >nul 2>&1
taskkill /F /IM "sa_vt.exe" >nul 2>&1

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "App\*" "%INSTALL_DIR%\" >nul
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy application files.
    pause
    exit /b 1
)

REM Install Windows Service
echo Installing Windows Service...
cd /d "%INSTALL_DIR%"
"%INSTALL_DIR%\ScreenshotService.exe" /install >nul 2>&1
if %errorLevel% neq 0 (
    echo WARNING: Service installation failed. You may need to install manually.
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\PCSMS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Sm_system.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\Sm_system.exe'; $Shortcut.Description = 'PCSMS - PC Screen Monitoring System'; $Shortcut.Save()"

REM Create start menu entry
echo Creating start menu entry...
set START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs\PCSMS
if not exist "%START_MENU%" mkdir "%START_MENU%"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\PCSMS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Sm_system.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\Sm_system.exe'; $Shortcut.Description = 'PCSMS - PC Screen Monitoring System'; $Shortcut.Save()"

REM Create uninstaller shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Uninstall PCSMS.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.Description = 'Uninstall PCSMS'; $Shortcut.Save()"

REM Copy uninstaller
copy "uninstall.bat" "%INSTALL_DIR%\uninstall.bat" >nul

REM Add to Windows Programs list
echo Adding to Windows Programs list...
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /v "DisplayName" /t REG_SZ /d "PCSMS - PC Screen Monitoring System" /f >nul
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /v "DisplayVersion" /t REG_SZ /d "1.0.0" /f >nul
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /v "Publisher" /t REG_SZ /d "Your Company Name" /f >nul
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PCSMS" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul

REM Set up Windows Firewall exceptions
echo Configuring Windows Firewall...
netsh advfirewall firewall add rule name="PCSMS Main App" dir=in action=allow program="%INSTALL_DIR%\Sm_system.exe" >nul 2>&1
netsh advfirewall firewall add rule name="PCSMS Screen Capture" dir=in action=allow program="%INSTALL_DIR%\ScreenCapturingApp.exe" >nul 2>&1

REM Start the service
echo Starting PCSMS service...
net start "Screenshot Capturing Service" >nul 2>&1

echo.
echo ===============================================
echo Installation completed successfully!
echo ===============================================
echo.
echo PCSMS has been installed to: %INSTALL_DIR%
echo.
echo Desktop shortcut: Created
echo Start menu entry: Created
echo Windows service: Installed
echo.
echo To start using PCSMS:
echo 1. Double-click the PCSMS icon on your desktop
echo 2. Enter your company code
echo 3. Activate your license (trial or premium)
echo 4. Login with your employee credentials
echo.
echo For support, please contact your system administrator.
echo.

REM Ask if user wants to start the application now
set /p START_NOW="Would you like to start PCSMS now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo Starting PCSMS...
    start "" "%INSTALL_DIR%\Sm_system.exe"
)

echo.
echo Installation log saved to: %INSTALL_DIR%\install.log
echo.
pause
