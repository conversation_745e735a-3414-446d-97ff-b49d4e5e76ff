﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace Custom_test
{
    public partial class MaterialTextBox : UserControl
    {
        Color _lineColor = Color.Gray;
        Color _lineFocusColor = Color.Orange;
        bool _numberOnly = false;
        public MaterialTextBox()
        {
            InitializeComponent();
            textBox1.GotFocus += TextBox1_GotFocus;
            textBox1.LostFocus += TextBox1_LostFocus;
            textBox1.KeyPress += TextBox1_KeyPress;
        }

        private void TextBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (_numberOnly)
            {
                if (!char.IsControl(e.<PERSON>har) && !char.IsDigit(e.<PERSON><PERSON>))
                {
                    e.<PERSON>led = true;
                }
            }
        }

       

        private void TextBox1_LostFocus(object sender, EventArgs e)
        {
            OnLostFocus(e);
        }

        private void TextBox1_GotFocus(object sender, EventArgs e)
        {
            OnGotFocus(e);
        }

        //GraphicsPath GetRoundPath(RectangleF Rect, int radius)
        //{
        //    float r2 = radius / 2f;
        //    GraphicsPath GraphPath = new GraphicsPath();

        //    GraphPath.AddArc(Rect.X, Rect.Y, radius, radius, 180, 90);
        //    GraphPath.AddLine(Rect.X + r2, Rect.Y, Rect.Width - r2, Rect.Y);
        //    GraphPath.AddArc(Rect.X + Rect.Width - radius, Rect.Y, radius, radius, 270, 90);
        //    GraphPath.AddLine(Rect.Width, Rect.Y + r2, Rect.Width, Rect.Height - r2);
        //    GraphPath.AddArc(Rect.X + Rect.Width - radius,
        //                     Rect.Y + Rect.Height - radius, radius, radius, 0, 90);
        //    GraphPath.AddLine(Rect.Width - r2, Rect.Height, Rect.X + r2, Rect.Height);
        //    GraphPath.AddArc(Rect.X, Rect.Y + Rect.Height - radius, radius, radius, 90, 90);
        //    GraphPath.AddLine(Rect.X, Rect.Height - r2, Rect.X, Rect.Y + r2);

        //    GraphPath.CloseFigure();
        //    return GraphPath;
        //}
        //protected override void OnPaint(PaintEventArgs e)
        //{
        //    base.OnPaint(e);
        //    RectangleF Rect = new RectangleF(0, 0, this.Width, this.Height);
        //    GraphicsPath GraphPath = GetRoundPath(Rect,7);

        //    this.Region = new Region(GraphPath);
        //    //using (Pen pen = new Pen(Color.CadetBlue, 1.75f))
        //    //{
        //    //    pen.Alignment = PenAlignment.Inset;
        //    //    e.Graphics.DrawPath(pen, GraphPath);
        //    //}
        //}


        [Category("MaterialTextbox")]
        public string MText { get => textBox1.Text; set => textBox1.Text = value; }
        [Category("MaterialTextbox")]
        public Font MFont { get => textBox1.Font; set => textBox1.Font = value; }
        [Category("MaterialTextbox")]
        public Color MTextColor { get => textBox1.ForeColor; set => textBox1.ForeColor = value; }
        [Category("MaterialTextbox")]
        public int MLineThiknes { get => panel1.Height; set => panel1.Height = value; }
        [Category("MaterialTextbox")]
        public Color MLineColor { get => panel1.BackColor; set => panel1.BackColor = value; }
        [Category("MaterialTextbox")]
        public Color MLineFocusColor { get =>_lineFocusColor ; set =>_lineFocusColor= value; }
        [Category("MaterialTextbox")]
        public Color MBackColor { get {return textBox1.BackColor; } set {
               if(value != Color.Transparent)
                {
                    textBox1.BackColor = value;
                    this.BackColor = value;
                }
            } }

        [Category("MaterialTextbox")]
        public bool MMultiline { get => textBox1.Multiline; set => textBox1.Multiline = value; }

        [Category("MaterialTextbox")]
        public bool MNumberOnly { get => _numberOnly; set => _numberOnly = value; }

        protected override void OnGotFocus(EventArgs e)
        {
            base.OnGotFocus(e);
            textBox1.Focus();
            _lineColor = panel1.BackColor;
            panel1.BackColor = _lineFocusColor;
        }
        protected override void OnLostFocus(EventArgs e)
        {
            base.OnLostFocus(e);
            panel1.BackColor = _lineColor;
        }

    }
}
