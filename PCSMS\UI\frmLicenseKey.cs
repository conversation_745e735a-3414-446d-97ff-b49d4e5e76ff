﻿using Gateway;
using LocalDataBank;
using System;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Utils;

namespace PCSMS.UI
{
    public partial class frmLicenseKey : Form
    {
        public event EventHandler OnCloseClicked;
        public event EventHandler OnSigninClicked;
        public event EventHandler OnSwitchCompanyClicked;
        public event EventHandler<string> OnLicenseActivated;
        bool isWorking = false;


        #region DropShadow
        //===================================*******============================

        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmLicenseKey()
        {
            InitializeComponent();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
        public void ShowForm()
        {
            base.Show();
            tbLicenseKey.Text = null;
            erLicenseKey.Text = null;
            tbLicenseKey.Focus();
            pbPremiumLoading.Visible = false;
            pbTrialLoading.Visible = false;
            //MessageBox.Show(App.GetCPUId());
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            OnCloseClicked?.Invoke(this,null);
        }

        private void btnLicenseActivate_Click(object sender, EventArgs e)
        {
            if (!isWorking)
            {
                erLicenseKey.Text = null;
                string licenseKeyPattern = @"^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}";
                if (!String.IsNullOrEmpty(tbLicenseKey.Text.Trim()) && Regex.IsMatch(tbLicenseKey.Text.Trim(), licenseKeyPattern))
                {
                    //server request 
                    var frmPcName = new frmChangeComputerName();
                    frmPcName.OnPCNameTaken += (pcName) => {
                        DoActivateLicense(tbLicenseKey.Text.Trim(), pcName);
                    };
                    frmPcName.ShowDialog();
                }
                else
                {
                    tbLicenseKey.Focus();
                    erLicenseKey.Text = "Invalid license key";
                }
            }
        }
        private void btnFreeTrialActivate_Click(object sender, EventArgs e)
        {
            if (!isWorking)
            {
                var frmPcName = new frmChangeComputerName();
                frmPcName.OnPCNameTaken += (pcName) => {
                    DoActivateLicense(null, pcName);
                };
                frmPcName.ShowDialog();
            }
        }
        private void btnSignin_Click(object sender, EventArgs e)
        {
            var user = SettingsDB.GetUserInfo();
            if (user == null || user.Id <= 0)
            {
                if (!isWorking)
                {
                    OnSigninClicked?.Invoke(this, null);
                } 
            }else
            {
                MessageBox.Show("You are currently logged in, Press cross button in the top right corner to go to user-interface.", "Warning..");
            }
        }

        private void DoActivateLicense(string licenseKey,string pcName)
        {
            //MessageBox.Show($"Licese key: {licenseKey}\nPC Name: {pcName}\nDeviceId: {App.GetCpuId()}\nCompanyCode: {SettingsDB.GetCompany().Code}");
            isWorking = true;
            if (licenseKey == null)
            {
                pbTrialLoading.Visible = true;
            }
            else
            {
                pbPremiumLoading.Visible = true;
            }
            var server = new Server();
            server.ActivateLicense(licenseKey, pcName, App.GetCpuId(), SettingsDB.GetCompany().Code, result => 
            {
                Invoke(new Action(() => {
                    try
                    {
                        isWorking = false;
                        pbTrialLoading.Visible = false;
                        pbPremiumLoading.Visible = false;
                        if (result.IsSuccess)
                        {
                            result.Content.CompanyCode = SettingsDB.GetCompany().Code;
                            SettingsDB.StoreLicense(result.Content);
                            MessageBox.Show(result.SuccessMessage, "Success..");
                            OnLicenceApplied("");
                        }
                        else
                        {
                            MessageBox.Show(result.ErrorMessage, "Warning..");
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e.Message);
                    }
                }));
            });
            
        }
        private void OnLicenceApplied(string licenseType)
        {
            OnLicenseActivated?.Invoke(this, licenseType);
        }

        private void lblSwitchCompany_Click(object sender, EventArgs e)
        {
            if (!isWorking)
            {
                OnSwitchCompanyClicked?.Invoke(this, null);
            }
        }

        private void frmLicenseKey_Load(object sender, EventArgs e)
        {

        }

        private void lblSwitchCompany_MouseDown(object sender, MouseEventArgs e)
        {
            lblSwitchCompany.ForeColor = Color.FromArgb(80, 133, 125);
        }

        private void lblSwitchCompany_MouseUp(object sender, MouseEventArgs e)
        {
            lblSwitchCompany.ForeColor = Color.FromArgb(115, 201, 188);
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
