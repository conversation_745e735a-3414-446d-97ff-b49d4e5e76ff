; PCSMS Desktop Application - Inno Setup Script
; This script creates a single setup.exe file for easy distribution

[Setup]
AppName=PCSMS - PC Screen Monitoring System
AppVersion=1.0.0
AppPublisher=Your Company Name
AppPublisherURL=https://yourcompany.com
AppSupportURL=https://yourcompany.com/support
AppUpdatesURL=https://yourcompany.com/updates
DefaultDirName={autopf}\PCSMS
DefaultGroupName=PCSMS
AllowNoIcons=yes
LicenseFile=LICENSE.txt
OutputDir=Output
OutputBaseFilename=PCSMS-Setup-v1.0
SetupIconFile=PCSMS_Icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1
Name: "startservice"; Description: "Start PCSMS service after installation"; GroupDescription: "Service Options"; Flags: checked

[Files]
; Main application files
Source: "PCSMS\bin\Release\Sm_system.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "PCSMS\bin\Release\*.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "PCSMS\bin\Release\*.config"; DestDir: "{app}"; Flags: ignoreversion

; Screen capturing application
Source: "ScreenCapturingApp\bin\Release\ScreenCapturingApp.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "ScreenCapturingApp\bin\Release\*.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "ScreenCapturingApp\bin\Release\*.config"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; Windows service
Source: "ScreenshotService\bin\Release\ScreenshotService.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "ScreenshotService\bin\Release\*.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "ScreenshotService\bin\Release\*.config"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; App reset utility
Source: "AppReset\bin\Release\AppReset.exe"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "AppReset\bin\Release\*.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; Documentation
Source: "README.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion skipifsourcedoesntexist
Source: "DEPLOYMENT_GUIDE.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion skipifsourcedoesntexist
Source: "DEBUG_SCREEN_CAPTURE.md"; DestDir: "{app}\Documentation"; Flags: ignoreversion skipifsourcedoesntexist

; Icon file
Source: "PCSMS\PCSMS_Icon.ico"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
Name: "{group}\PCSMS"; Filename: "{app}\Sm_system.exe"; IconFilename: "{app}\PCSMS_Icon.ico"
Name: "{group}\{cm:UninstallProgram,PCSMS}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\PCSMS"; Filename: "{app}\Sm_system.exe"; IconFilename: "{app}\PCSMS_Icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\PCSMS"; Filename: "{app}\Sm_system.exe"; Tasks: quicklaunchicon

[Run]
; Install Windows service
Filename: "{app}\ScreenshotService.exe"; Parameters: "/install"; StatusMsg: "Installing PCSMS service..."; Flags: runhidden
; Start the service
Filename: "net"; Parameters: "start ""Screenshot Capturing Service"""; StatusMsg: "Starting PCSMS service..."; Flags: runhidden; Tasks: startservice
; Ask to run application
Filename: "{app}\Sm_system.exe"; Description: "{cm:LaunchProgram,PCSMS}"; Flags: nowait postinstall skipifsilent

[UninstallRun]
; Stop and uninstall service
Filename: "net"; Parameters: "stop ""Screenshot Capturing Service"""; Flags: runhidden
Filename: "{app}\ScreenshotService.exe"; Parameters: "/uninstall"; Flags: runhidden

[Code]
// Custom installation logic
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // Add Windows Firewall exceptions
    Exec('netsh', 'advfirewall firewall add rule name="PCSMS Main App" dir=in action=allow program="' + ExpandConstant('{app}') + '\Sm_system.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    Exec('netsh', 'advfirewall firewall add rule name="PCSMS Screen Capture" dir=in action=allow program="' + ExpandConstant('{app}') + '\ScreenCapturingApp.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
var
  ResultCode: Integer;
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // Remove Windows Firewall rules
    Exec('netsh', 'advfirewall firewall delete rule name="PCSMS Main App"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    Exec('netsh', 'advfirewall firewall delete rule name="PCSMS Screen Capture"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    
    // Kill any running processes
    Exec('taskkill', '/F /IM "Sm_system.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    Exec('taskkill', '/F /IM "ScreenCapturingApp.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    Exec('taskkill', '/F /IM "sa_vt.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
  end;
end;

// Check for .NET Framework
function InitializeSetup(): Boolean;
var
  NetFrameWorkInstalled : Boolean;
  Result1 : Boolean;
begin
  Result := true;
  NetFrameWorkInstalled := RegKeyExists(HKLM,'SOFTWARE\Microsoft\.NETFramework\policy\v4.0');
  
  if NetFrameWorkInstalled = false then
  begin
    Result1 := MsgBox('This application requires Microsoft .NET Framework 4.5.1 or higher. Please install .NET Framework and run this setup again. Do you want to continue anyway?', mbConfirmation, MB_YESNO) = idYes;
    if Result1 = false then
    begin
      Result := false;
    end;
  end;
end;
