# PCSMS Desktop Application - PC Screen Monitoring Client

## Overview

**PCSMS Desktop Application** is a Windows-based employee monitoring client that captures screenshots, tracks keystrokes, and monitors mouse activity on employee computers. It works in conjunction with the PCSMS web application to provide comprehensive employee monitoring capabilities.

## System Architecture

### Multi-Component Structure

The desktop application consists of several interconnected components:

1. **Main UI Application** (`PCSMS.exe`) - User interface for login and configuration
2. **Screen Capturing App** (`ScreenCapturingApp.exe`) - Background screenshot capture
3. **Screenshot Service** (`ScreenshotService.exe`) - Windows service for continuous monitoring
4. **Local Data Bank** - LiteDB-based local storage for offline data
5. **Gateway** - API communication layer with web server

## Technology Stack

- **Framework**: .NET Framework 4.6, Windows Forms
- **Local Database**: LiteDB 4.1.4
- **API Communication**: RestSharp 105.2.3
- **Real-time Communication**: SignalR Client 2.3.0
- **Input Monitoring**: MouseKeyHook 5.6.0
- **JSON Processing**: Newtonsoft.Json 6.0.4

## Key Components

### 1. Main Application (`PCSMS/`)

- **Purpose**: User authentication and configuration interface
- **Features**:
  - Company code validation
  - License activation (Trial/Premium)
  - User login with email/password
  - Dashboard for monitoring status
  - Settings management

### 2. Screen Capturing App (`ScreenCapturingApp/`)

- **Purpose**: Background screenshot capture and data collection
- **Features**:
  - Automated screenshot capture at configured intervals
  - Keystroke and mouse click tracking
  - Local data storage with LiteDB
  - Automatic data upload to web server
  - Random interval support for stealth monitoring

### 3. Screenshot Service (`ScreenshotService/`)

- **Purpose**: Windows service for continuous operation
- **Features**:
  - Runs as Windows service
  - Automatically launches capture app (`sa_vt.exe`)
  - Process monitoring and restart capability
  - UAC bypass for service operations

### 4. Local Data Storage (`LocalDataBank/`)

- **Purpose**: Offline data persistence
- **Components**:
  - `ScreenshotDB.cs` - Screenshot storage and retrieval
  - `SettingsDB.cs` - Configuration and user data storage
  - LiteDB database files in application data folder

### 5. Gateway (`Gateway/`)

- **Purpose**: Web API communication
- **Features**:
  - User authentication
  - License activation
  - Screenshot upload
  - Device registration
  - Company validation

## Authentication Flow

### 1. Company Code Entry

```
User Input → Company Code Validation → Server Verification → Local Storage
```

### 2. License Activation

```
License Type Selection → Device Name Entry → Server Activation → Local License Storage
```

- **Trial License**: Automatic activation, 10 device limit
- **Premium License**: Requires license key format `XXXX-XXXX-XXXX-XXXX-XXXX`

### 3. User Login

```
Email/Password → Server Authentication → User Info Storage → Dashboard Access
```

## Data Models

### Core Entities

```csharp
Company
├── Id: int
└── Code: string (8+ characters)

License
├── Id: int
├── CompanyCode: string
├── LicenseKey: string
├── ActivatedOn: DateTime
├── ExpiryDate: DateTime
└── Status: string (Active/Expired/Inactive)

UserInfo
├── Id: int
├── FirstName: string
├── LastName: string
├── Email: string
├── Designation: string
└── CompanyName: string

ScreenshotImage
├── Id: int
├── UserId: int
├── DeviceUniqueId: string
├── ScreenShot: string (Base64)
├── CapturedOn: DateTime
├── KeyStroke: int
└── MouseClick: int

WorkSchedule
├── Id: int
├── StartTime: DateTime
├── EndTime: DateTime
├── IntervalInMinute: int
├── WorkingDays: Dictionary<string,bool>
├── DeviceName: string
└── IsRandom: string (Y/N)
```

## Screenshot Capture Process

### 1. Validation Chain

```
License Active? → User Logged In? → Work Schedule Valid? → Working Hours? → Capture Screenshot
```

### 2. Capture Flow

```
Timer Trigger → Screen Capture → Keystroke/Mouse Data → Base64 Conversion → Local Storage → Upload Queue
```

### 3. Upload Process

```
Local Database → Batch Retrieval → API Upload → Success Confirmation → Local Cleanup
```

## Configuration

### Local Storage Locations

- **Database Files**: `%AppData%\[ApplicationName]\`
- **Screenshot DB**: `str_dt.dll` (LiteDB file)
- **Settings DB**: Application configuration and user data

### Default Settings

- **Screenshot Interval**: 60 seconds (1 minute)
- **Upload Interval**: 30 seconds
- **Random Interval**: 1-5 minutes (when enabled)
- **Image Format**: JPEG with Base64 encoding

## API Endpoints

### Authentication

```
POST /Api/Authenticate/LogIn
POST /api/Company/IsCompanyCodeValid/{code}
POST /api/License/ActivateLicense
```

### Device Management

```
POST /api/Device/RegisterDevice
POST /api/Device/SaveScreenCapture
GET  /api/Device/GetDeviceSchedule
```

## Installation & Deployment

### Prerequisites

- Windows 7/8/10/11
- .NET Framework 4.6 or higher
- Administrator privileges (for service installation)
- Internet connectivity for server communication

### Installation Process

1. Download `PCSMS-App.exe` from company portal
2. Run installer with administrator privileges
3. Enter company code provided by administrator
4. Activate license (Trial or Premium)
5. Login with employee credentials
6. Application starts monitoring automatically

### Service Installation

```bash
# Install as Windows Service
installutil ScreenshotService.exe

# Start service
net start "Screenshot Capturing Service"
```

## Troubleshooting

### Common Issues

#### 1. Screenshots Not Capturing

**Possible Causes:**

- License expired or inactive
- User not logged in
- Outside working hours
- Work schedule not configured
- Insufficient permissions

**Debug Steps:**

- Check license status in dashboard
- Verify user login status
- Confirm work schedule settings
- Check Windows event logs
- Run as administrator

#### 2. Data Not Uploading

**Possible Causes:**

- Network connectivity issues
- Server authentication problems
- API endpoint unavailable
- Local database corruption

**Debug Steps:**

- Test internet connectivity
- Check API server status
- Verify authentication tokens
- Clear local database cache

#### 3. Service Not Starting

**Possible Causes:**

- Insufficient privileges
- Service not installed properly
- Dependency issues
- Port conflicts

**Debug Steps:**

- Run as administrator
- Reinstall service
- Check Windows Services panel
- Review system event logs

### Debug Mode

#### Enable Logging

1. Check local application data folder for log files
2. Monitor Windows Event Viewer for application errors
3. Use Process Monitor to track file/registry access
4. Enable debug mode in configuration

#### Manual Testing

```csharp
// Test screenshot capture manually
var screenshot = new Screenshot();
var bitmap = screenshot.FullScreenshot();
var base64 = Screenshot.ImageToBase64String(bitmap);
```

## File Structure

```
PCSMS-Desktop/
├── PCSMS/                    # Main UI application
│   ├── UI/                   # Windows Forms
│   ├── Program.cs            # Application entry point
│   └── App.config            # Configuration
├── ScreenCapturingApp/       # Background capture app
│   ├── ScreenshotHandler.cs  # Core capture logic
│   ├── Program.cs            # Capture app entry
│   └── Notification.cs       # System tray notifications
├── ScreenshotService/        # Windows service
│   ├── CapturingService.cs   # Service implementation
│   └── AppLauncher.cs        # Process launcher
├── LocalDataBank/            # Data persistence
│   ├── ScreenshotDB.cs       # Screenshot storage
│   └── SettingsDB.cs         # Configuration storage
├── Gateway/                  # API communication
│   └── Server.cs             # REST API client
├── MDLS/                     # Data models
├── Win_Mda/                  # Screen capture utilities
└── uti_ls/                   # Utility functions
```

## Security Features

- **Device Identification**: CPU-based unique device ID
- **Encrypted Storage**: Local data encryption
- **Secure Communication**: HTTPS API communication
- **Authentication Tokens**: JWT-based authentication
- **Multi-tenant Isolation**: Company-based data separation

## Performance Considerations

- **Memory Management**: Automatic bitmap disposal
- **Storage Optimization**: Compressed JPEG screenshots
- **Network Efficiency**: Batch upload mechanism
- **CPU Usage**: Configurable capture intervals
- **Disk Space**: Automatic cleanup after successful uploads

## Monitoring Features

- **Real-time Capture**: Configurable screenshot intervals
- **Activity Tracking**: Keystroke and mouse click counting
- **Schedule-based Monitoring**: Working hours enforcement
- **Random Intervals**: Stealth monitoring capability
- **Offline Support**: Local storage with sync capability
