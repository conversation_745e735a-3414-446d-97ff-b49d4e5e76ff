﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;

namespace Utils
{
    public class NIC
    {
        [System.Runtime.InteropServices.DllImport("wininet.dll")]
        private extern static bool InternetGetConnectedState(out int Description, int ReservedValue);




        public static bool IsInternetConnected()
        {
            return InternetGetConnectedState(out int desc, 0);
        }
        public static string GetMACAddress()
        {
            try
            {
                ManagementObjectSearcher objMOS = new ManagementObjectSearcher("Select * FROM Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection objMOC = objMOS.Get();
                string macAddress = String.Empty;
                foreach (ManagementObject objMO in objMOC)
                {
                    object tempMacAddrObj = objMO["MacAddress"];

                    if (tempMacAddrObj == null) //Skip objects without a MACAddress
                    {
                        continue;
                    }
                    if (macAddress == String.Empty) // only return MAC Address from first card that has a MAC Address
                    {
                        macAddress = tempMacAddrObj.ToString();
                    }
                    objMO.Dispose();
                }
                macAddress = macAddress.Replace(":", "");
                return macAddress;
            }
            catch (Exception ex)
            {
                return "mac address not found";
            }
        }

    }
}
