﻿using System;
using System.Windows.Forms;
using LocalDataBank;

namespace PCSMS.UI
{
    public partial class viewLisenceKey : UserControl
    {
        public event EventHandler OnChangeLicenseClicked;


        public viewLisenceKey()
        {
            InitializeComponent();
        }
        public void BringToFront()
        {
            base.BringToFront();
            RefreshData();
        }

        public void RefreshData()
        {
            try
            {
                var license = SettingsDB.GetLicense();

                if (license != null)
                {
                    lblLicense.Text = license.LicenseKey.Equals("Free trial") ? "Free trial" : license.LicenseKey.Substring(0, 4) + "-####-####-####-" + license.LicenseKey.Substring(20);
                    lblActivationDate.Text = license.ActivatedOn.ToString("dd/MM/yyyy"); //"MM/dd/yyyy hh:mm tt"
                    lblExpirationDate.Text = license.ExpiryDate.ToString("dd/MM/yyyy");
                    var days = (license.ExpiryDate - DateTime.Now).Days;
                    lblStatus.Text = license.Status;
                }
                else
                {
                    lblLicense.Text = "N/A";
                    lblActivationDate.Text = "N/A";
                    lblExpirationDate.Text = "N/A";
                    lblStatus.Text = "N/A";
                }
            }
            catch (Exception) { }
        }

        private void btnChangeLicenseKey_Click(object sender, EventArgs e)
        {
            OnChangeLicenseClicked?.Invoke(this,null);
        }

        
    }
}
