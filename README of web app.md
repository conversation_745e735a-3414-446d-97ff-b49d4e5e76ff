# PCSMS - PC Screen Monitoring System

## Overview

**PCSMS (PC Screen Monitoring System)** is a comprehensive multi-tenant employee monitoring solution that enables organizations to track and monitor employee computer activities through automated screen capture, keystroke logging, and mouse click tracking.

## System Architecture

### Multi-Tenant Structure

The system operates on a **two-tier multi-tenancy model**:

1. **Service Provider (SP)** - Master/Parent Level

   - System administrators and resellers
   - Manage multiple client companies
   - Issue and manage licenses
   - Organizational structure with branches and designations

2. **Company (CP)** - Tenant/Child Level
   - End customers using the monitoring system
   - Monitor their own employees' devices
   - Isolated data per company
   - Individual user and device management

## Technology Stack

- **Backend**: ASP.NET MVC 5, Web API 2, Entity Framework 6
- **Frontend**: AngularJS 1.x (Separate SPAs for SP and Company)
- **Database**: SQL Server
- **Authentication**: Custom JWT implementation
- **Real-time Communication**: SignalR
- **Desktop App**: Windows executable for screen capture

## Key Components

### 1. Web Application

- **Service Provider Portal**: `/SProvider/` - Admin interface for managing companies and licenses
- **Company Portal**: `/Company/` - Client interface for monitoring employees and viewing reports
- **API Layer**: RESTful Web API for desktop app communication

### 2. Desktop Application

- **Location**: `/Download-app/PCSMS-App.exe`
- **Purpose**: Installed on employee computers for automated monitoring
- **Features**:
  - Automatic screen capture at configured intervals
  - Mouse click and keystroke logging
  - Device registration and authentication
  - Real-time data transmission to web server

## Database Schema

### Service Provider Entities

```
SP_Profile      - Service provider organization details
SP_User         - Service provider staff/administrators
SP_Branch       - Different branches of service provider
SP_Designation  - Job roles within service provider
SP_Token        - Authentication tokens for SP users
```

### Company Entities

```
CP_Profile        - Company registration and profile information
CP_User           - Company employees being monitored
CP_Device         - Employee computers/devices being monitored
CP_License        - Licensing for device monitoring limits
CP_License_Period - License validity periods
CP_ScreenCapture  - Captured screenshots with metadata
CP_Token          - Authentication tokens for companies
CP_Device_License - Device-license mapping
CP_Device_Group   - Device grouping for management
CP_Device_Schedule- Monitoring schedules per device
CP_User_LSession  - User login sessions
```

### Shared Entities

```
AccessType    - User access levels and permissions
CompanyType   - Types of companies (categorization)
```

## Business Logic Flow

### 1. Company Registration

1. Company registers through web portal
2. Downloads desktop application (`PCSMS-App.exe`)
3. System automatically creates **Trial License** (10 devices limit)
4. Company can upgrade to paid licenses for more devices

### 2. Device Registration & Monitoring

1. Desktop app installed on employee computers
2. Device registers with unique identifier
3. System validates license availability
4. Automated monitoring begins:
   - Screen captures at intervals
   - Mouse click tracking
   - Keystroke logging
   - Data transmitted to web server

### 3. License Management

- **Trial License**: Auto-generated, 10 device limit
- **Paid License**: Purchased licenses with higher device limits
- **License Status**: Active, In-Active, Expired, Requested
- **Device Mapping**: Each device must be linked to active license

### 4. Authentication System

- **Dual Authentication**: Separate flows for Service Providers vs Companies
- **JWT Tokens**: Different token prefixes ("SProviderPCSMS" vs company tokens)
- **Role-based Access**: Service providers manage system, companies view own data

## API Endpoints

### Device Management

```
POST /api/Device/RegisterDevice          - Register new device
POST /api/Device/SaveScreenCapture       - Save captured screenshots
GET  /api/Device/GetDeviceListByCompanyId/{id} - Get company devices
GET  /api/Device/GetScreenshotsListByDeviceUniqueId/{id} - Get device screenshots
```

### Authentication

```
POST /Api/Authenticate/LogIn             - User authentication
```

### Company Management

```
POST /Api/CP_Profile/SignUp              - Company registration
GET  /Api/CP_Profile/GetCompanyDetails/{id} - Get company information
```

## File Structure

```
PCSMS/
├── PCSMS.Models/           # Database entities and DbContext
│   ├── Models_SProvider/   # Service provider entities
│   ├── Models_Company/     # Company entities
│   ├── Models_Shared/      # Shared entities
│   └── PCSMSDbContext.cs   # Entity Framework context
├── PCSMS.Services/         # Business logic layer
│   ├── Services_SProvider/ # SP business services
│   ├── Services_Company/   # Company business services
│   └── Services_Shared/    # Shared services
├── PCSMS.Repository/       # Data access layer
├── PCSMS/                  # Web application
│   ├── SProvider/          # Service provider frontend (AngularJS)
│   ├── Company/            # Company frontend (AngularJS)
│   ├── Controllers/        # Web API controllers
│   └── Download-app/       # Desktop application files
└── packages/               # NuGet packages
```

## Configuration

### Database Connection

```xml
<connectionStrings>
  <add name="Cn" connectionString="..." providerName="System.Data.SqlClient" />
</connectionStrings>
```

### App Settings

```xml
<appSettings>
  <add key="downloadUrl" value="pcsms.bacbonprojects.com/Download-app/PCSMS-App.exe" />
  <add key="AuthTokenExpiry" value="24" />
</appSettings>
```

## Development Setup

### Prerequisites

- Visual Studio 2015+
- .NET Framework 4.6
- SQL Server 2012+
- IIS Express

### Steps

1. Clone repository
2. Restore NuGet packages
3. Update connection string in `Web.config`
4. Run Entity Framework migrations
5. Build and run solution
6. Access portals:
   - Service Provider: `/SProvider/`
   - Company: `/Company/`

## Deployment

### Web Application

- Publish to IIS server
- Ensure SQL Server connectivity
- Configure file upload permissions for screenshots

### Desktop Application

- Build Windows executable
- Deploy to `/Download-app/` folder
- Configure API endpoints in desktop app

## Security Features

- JWT-based authentication
- Multi-tenant data isolation
- Role-based access control
- Secure API endpoints
- Encrypted password storage

## Monitoring Features

- Real-time screen capture
- Mouse click tracking
- Keystroke logging
- Device activity monitoring
- Automated reporting
- Screenshot management and storage

## Desktop Application Details

### Installation Process

1. Company registers on web portal
2. Downloads `PCSMS-App.exe` from `/Download-app/`
3. Installs on employee computers
4. App automatically registers device with server
5. Begins monitoring based on configured schedule

### Data Transmission

- Screenshots converted to Base64 and transmitted via API
- Mouse clicks and keystrokes logged with timestamps
- Real-time data sync with web server
- Automatic retry mechanism for failed transmissions

### API Communication

```
POST /api/Device/RegisterDevice     - Initial device registration
POST /api/Device/SaveScreenCapture  - Upload captured data
GET  /api/Device/GetDeviceSchedule  - Retrieve monitoring schedule
```

## License Types

- **Trial**: 10 devices, auto-generated on first device registration
- **Paid**: Custom device limits based on purchase
- **Status Tracking**: Active, Expired, Renewal requests
- **Device Mapping**: Each device must be linked to active license

## Troubleshooting

### Common Issues

1. **Device Registration Failed**: Check license availability and network connectivity
2. **Screenshots Not Uploading**: Verify API endpoints and authentication tokens
3. **License Expired**: Contact service provider for renewal
4. **Desktop App Not Starting**: Check Windows compatibility and permissions

### Log Files

- Web application logs: `/Logs/` folder
- Desktop application logs: Local application data folder

---

## Support & Maintenance

For technical support and maintenance, refer to the development team or system administrators.

**Last Updated**: 2025-01-13
