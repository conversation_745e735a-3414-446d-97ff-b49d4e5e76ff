﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Bunifu.Framework.UI;
using Gateway;
using LocalDataBank;
using ObjectModels;
using Utils;

namespace PCSMS.UI
{
    public partial class frmUserDetails : Form
    {
        public event EventHandler OnCloseClicked;
        public event EventHandler OnUserInfoUpdated;

        bool isWaiting = false;




        #region DropShadow
        //===================================*******============================
      



        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmUserDetails()
        {
            InitializeComponent();
            pbProfilePic.LoadCompleted += PbProfilePic_LoadCompleted;
        }

      
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
        private void btnClose_Click(object sender, EventArgs e)
        {
            if(!isWaiting)
                 OnCloseClicked?.Invoke(this, null);
        }
        internal void ShowForm()
        {
            base.Show();
            pbProfilePic.Image = null;
            SetUserInfoToUI();
        }
        private void btnEdit_Click(object sender, EventArgs e)
        {
            var frmUpdateInfo = new frmUpdateUserDetails();
            frmUpdateInfo.OnUserDetailsUpdated += FrmUpdateInfo_OnUserDetailsUpdated;
            frmUpdateInfo.ShowDialog();
        }
        private void FrmUpdateInfo_OnUserDetailsUpdated(object sender, EventArgs e)
        {
            OnUserInfoUpdated?.Invoke(this, e);
            SetUserInfoToUI();
        }

        private void SetUserInfoToUI()
        {
            var user = SettingsDB.GetUserInfo();
            if (user != null)
            {
                lblFirstName.Text = user.FirstName;
                lblLastName.Text= user.LastName;
                SetGender(user.Gender);
                lblBirthday.Text = user.BirthDate;
                lblMobile.Text = user.Mobile;
                lblAddress.Text = user.Address;
                if(user.ProfilePic !=null)
                {
                    loadProfilePic(user.ProfilePic);
                    DownloadImage(user);
                }
                else
                {
                    DownloadImage(user);
                }
            }
        }

       

        private void loadProfilePic(byte[] profilePic)
        {
            try
            {
              //  MemoryStream memoryStream = new MemoryStream(profilePic);
                pbProfilePic.Image = ByteToImage(profilePic);
                DownloadImage(SettingsDB.GetUserInfo());
            }
            catch (Exception)
            {
                DownloadImage(SettingsDB.GetUserInfo());
            }
        }
        private void DownloadImage(UserInfo user)
        {
            try
            {
                if (NIC.IsInternetConnected())
                {
                    if (!string.IsNullOrEmpty(user.Photo))
                    {
                        pbProfilePic.LoadAsync(Server.BaseUrl+"Company_Images/User_Images/" + user.Photo);
                    }
                    else
                    {
                        pbProfilePic.Image = null;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
        private void PbProfilePic_LoadCompleted(object sender, AsyncCompletedEventArgs e)
        {
            if (pbProfilePic.Image != null)
            {
                var imgData = ImageToByteArray(pbProfilePic.Image);
                var user = SettingsDB.GetUserInfo();
                user.ProfilePic = imgData;
                SettingsDB.StoreUserInfo(user);
            }
        }




        public byte[] ImageToByteArray(System.Drawing.Image imageIn)
        {
            MemoryStream ms = new MemoryStream();
            imageIn.Save(ms, System.Drawing.Imaging.ImageFormat.Gif);
            return ms.ToArray();
        }
        public static Bitmap ByteToImage(byte[] blob)
        {
            MemoryStream mStream = new MemoryStream();
            byte[] pData = blob;
            mStream.Write(pData, 0, Convert.ToInt32(pData.Length));
            Bitmap bm = new Bitmap(mStream, false);
            mStream.Dispose();
            return bm;
        }



        private void SetGender(string gender)
        {
            if (gender != null)
            {
                if (gender.Equals("Male"))
                {
                    cbMale.Checked = true;
                    cbFemale.Checked = false;
                }
                else
                {
                    cbMale.Checked = false;
                    cbFemale.Checked = true;
                }
            }else
            {
                cbMale.Checked = false;
                cbFemale.Checked = false;
            }
        }

        private void btnBrowse_Click(object sender, EventArgs e)
        {
           if(!isWaiting)
            {
                // open file dialog   
                OpenFileDialog open = new OpenFileDialog();
                // image filters  
                open.Filter = "Image Files(*.jpg; *.jpeg;*.png)|*.jpg; *.jpeg; *.png";
                if (open.ShowDialog() == DialogResult.OK)
                {
                    // display image in picture box  
                   // pbProfilePic.Image = new Bitmap(open.FileName);
                    // image file path  
                    DoUploadImage(open.FileName);
                }
            }
        }

        private void DoUploadImage(string path)
        {
            pbLoading.Visible = true;
            isWaiting = true;

            try
            {
                Server server = new Server();
                server.UploadImage(path, SettingsDB.GetUserInfo().Id, result =>
                {
                    Invoke(new Action(() =>
                    {
                        pbLoading.Visible = false;
                        isWaiting = false;
                        if (result.IsSuccess)
                        {
                            var user = SettingsDB.GetUserInfo();
                            user.Photo = result.Content;
                            SettingsDB.StoreUserInfo(user);
                            DownloadImage(user);
                            MessageBox.Show("Image successfully uploaded", "Success..");
                        }
                        else
                        {
                            MessageBox.Show(result.ErrorMessage, "Warning..");
                        }
                    }));
                });
            }
            catch (Exception)
            {
                pbLoading.Visible = false;
                isWaiting = false;
            }
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
