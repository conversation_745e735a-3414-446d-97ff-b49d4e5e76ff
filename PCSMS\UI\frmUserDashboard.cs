﻿using LocalDataBank;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PCSMS.UI
{
    public partial class frmUserDashboard : Form
    {

        public event EventHandler OnCloseClicked;
        public event EventHandler OnChangeLicenseClicked;
        public event EventHandler OnSignOutSuccess;
        public event EventHandler OnChangePasswordClicked;
        public event EventHandler OnUserDetailsClicked;



        #region DropShadow
        //===================================*******============================
        // Make Draggable from
        public const int WM_NCLBUTTONDOWN = 0xA1;
        public const int HT_CAPTION = 0x2;
        [DllImport("user32.dll")]
        public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);
        [DllImport("user32.dll")]
        public static extern bool ReleaseCapture();
        private void frmUserDashboard_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                ReleaseCapture();
                SendMessage(Handle, WM_NCLBUTTONDOWN, HT_CAPTION, 0);
            }
        }




        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmUserDashboard()
        {
            InitializeComponent();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
      

        //============== events ===============//
        private void frmUserDashboard_Load(object sender, EventArgs e)
        {
            viewUserInfo1.OnSignOutSuccess += OnSignOutSuccess;
            viewUserInfo1.OnChangePasswordClicked += this.OnChangePasswordClicked;
            viewUserInfo1.OnUserDetailsClicked += this.OnUserDetailsClicked;
            viewLisenceKey1.OnChangeLicenseClicked += this.OnChangeLicenseClicked;
            SetTabSelected(SelectedTab.UserInfo);

            // file watcher
            try
            {
                FileSystemWatcher watcher = new FileSystemWatcher();
                watcher.Path = SettingsDB.commonApplicationData.ApplicationFolderPath;
                watcher.Filter = "win_st.dll";
                watcher.NotifyFilter = NotifyFilters.LastWrite;
                watcher.Changed += Watcher_Changed;
                watcher.EnableRaisingEvents = true;
            }
            catch (Exception) { }
        }

        private void Watcher_Changed(object sender, FileSystemEventArgs e)
        {
            if(e.ChangeType == WatcherChangeTypes.Changed)
            {
                this.Invoke(new System.Threading.ThreadStart(() => {
                    viewWorkSchedule1.RefreshData();
                    viewLisenceKey1.RefreshData();
                }));
            }
        }

   
        private void btnClose_Click(object sender, EventArgs e)
        {
            OnCloseClicked?.Invoke(this,null);
        }



        private void btnUserInfo_Click(object sender, EventArgs e)
        {
            SetTabSelected(SelectedTab.UserInfo);
        }
        private void btnWorkSchedule_Click(object sender, EventArgs e)
        {
            SetTabSelected(SelectedTab.WorkSchedule);
        }
        private void btnLicense_Click(object sender, EventArgs e)
        {
            SetTabSelected(SelectedTab.LicenseKey);
        }





        //============== private method ============//
        public void SetTabSelected(SelectedTab selectedTab)
        {
            if (selectedTab == SelectedTab.UserInfo)
            {
                btnUserInfo.selected = true;
                btnWorkSchedule.selected = false;
                btnLicense.selected = false;
                viewUserInfo1.BringToFront();
            }
            else if (selectedTab == SelectedTab.WorkSchedule)
            {
                btnUserInfo.selected = false;
                btnWorkSchedule.selected = true;
                btnLicense.selected = false;
                viewWorkSchedule1.BringToFront();
            }
            else
            {
                btnUserInfo.selected = false;
                btnWorkSchedule.selected = false;
                btnLicense.selected = true;
                viewLisenceKey1.BringToFront();
            }
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }

    public enum SelectedTab
    {
        UserInfo,
        WorkSchedule,
        LicenseKey
    }
}
