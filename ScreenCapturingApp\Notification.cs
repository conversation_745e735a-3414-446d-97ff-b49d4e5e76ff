﻿using Gateway;
using LocalDataBank;
using Microsoft.AspNet.SignalR.Client;
using ObjectModels;
using System;
using System.Net.Http;
using System.Windows.Forms;
using Utils;

namespace ScreenCapturingApp
{
    public partial class Notification : Form
    {
        Timer timer;
        //const string ServerURI = "http://testpcsms.akhtar3rdeye.com"; 
        const string HubName = "notificationHub";
        private IHubProxy HubProxy { get; set; }
        private HubConnection Connection { get; set; }

        bool _connected = false;
        bool isHubConnected { get { return _connected; } set {
                _connected = value;
                if(value == true)
                {
                    OnHubConnected();
                }
            }
        }

       
        public Notification()
        {
            InitializeComponent();
            this.WindowState = FormWindowState.Minimized;
        }
        private void Notification_Load(object sender, EventArgs e)
        {
            InitConnection();

            timer = new Timer();
            timer.Interval = 8000;
            timer.Enabled = true;
            timer.Tick += Timer_Tick;
            timer.Start();
            ConnectAsync();

            this.Hide();
        }



        // initialize hub
        private void InitConnection()
        {
            Connection = new HubConnection(Server.BaseUrl);
            HubProxy = Connection.CreateHubProxy(HubName);
            Connection.Error += Connection_Error;
            Connection.Closed += Connection_Closed;
            Connection.ConnectionSlow += Connection_ConnectionSlow;
            Connection.Reconnecting += Connection_Reconnecting;
            Connection.Reconnected += Connection_Reconnected;
            
            // event hanlder
            InitHubEventHandler();
        }
        private void InitHubEventHandler()
        {
            HubProxy.On<int>("onScheduleChange", onScheduleChanged);
            HubProxy.On<int>("onLicenseStatusTransition", onLicenseChanged);
        }
        private void Timer_Tick(object sender, EventArgs e)
        {
            if (!isHubConnected)
                ConnectAsync();


            if (this.Visible)
                this.Hide();
        }



        // hub connection
        private async void ConnectAsync()
        {
            try
            {
                await Connection.Start();
                isHubConnected = true;

                //if (SettingsDB.GetUserInfo() != null)
                //{
                //    await HubProxy.Invoke("GetChanges", App.GetCpuId(), SettingsDB.GetUserInfo().Id);
                //}
                return;
            }
            catch (HttpRequestException)
            {
                isHubConnected = false;
                return;
            }
            catch(Exception)
            {
                isHubConnected = false;
                return;
            }
        }
        private void Connection_Closed()
        {
            isHubConnected = false;
        }
        private void Connection_Error(Exception obj)
        {
            isHubConnected = false;
        }
        private void Connection_Reconnected()
        {
            isHubConnected = true;
        }
        private void Connection_Reconnecting()
        {
            isHubConnected = false;
        }
        private void Connection_ConnectionSlow()
        {
        }



        // invoked by hub
        private void OnHubConnected()
        {
            var schedule = SettingsDB.GetWorkSchedule();
            if (SettingsDB.GetUserInfo() != null && schedule != null)
            {
                UpdateScheduleAndLicense(schedule.Id, CalledBy.OnHubConnected);
            }
            schedule = null;
        }
        private void onScheduleChanged(int deviceId)
        {
            var schedule = SettingsDB.GetWorkSchedule();
            if (SettingsDB.GetUserInfo() != null && schedule != null && schedule.Id == deviceId)
            {
                UpdateScheduleAndLicense(deviceId,CalledBy.OnScheduleChanged);
            }
            schedule = null;
        }
        private void onLicenseChanged(int licenseId)
        {
            var license = SettingsDB.GetLicense();
            if (SettingsDB.GetUserInfo() != null && license != null && license.Id == licenseId)
            {
                var schedule = SettingsDB.GetWorkSchedule();
                if(schedule != null)
                {
                    UpdateScheduleAndLicense(schedule.Id,CalledBy.OnLicenseChanged);
                }
                schedule = null;
            }
            license = null;
        }



        bool _working = false;
        private void UpdateScheduleAndLicense(int deviceId, CalledBy calledBy)
        {
            if (!_working)
            {
                try
                {
                    _working = true;

                    Server server = new Server();
                    server.GetDeviceAndLicenseDetails(deviceId, result => Invoke(new Action(() => {
                        _working = false;
                        if(result.IsSuccess && result.Content != null)
                        {
                            if(result.Content.deviceObj != null)
                            {
                                SettingsDB.UpdateWorkingSchedule(result.Content.deviceObj);
                                if(calledBy == CalledBy.OnScheduleChanged)
                                {
                                     PushNotification.PushNotification.ShowNotification("PC Screen Monitoring System", "Your schedule has been modified by " + SettingsDB.GetUserInfo().CompanyName +". Please, check dashboard for more details.", 10, runDashboardApp);
                                }
                            }

                            if (result.Content.licenseObj != null)
                            {
                                var license = result.Content.licenseObj;
                                license.CompanyCode = SettingsDB.GetCompany().Code;
                                SettingsDB.StoreLicense(license);

                                if(calledBy == CalledBy.OnLicenseChanged)
                                {
                                    if (license.Status.Equals("Active"))
                                    {
                                        PushNotification.PushNotification.ShowNotification("PC Screen Monitoring System", "Your license has been renewed. Please, check dashboard for more details.", 10, runDashboardApp);
                                    }
                                }

                                if(license.Status.Equals("Expired"))
                                {
                                    PushNotification.PushNotification.ShowNotification("PC Screen Monitoring System", "Your License has expired, Please renew this license or purchase new license.", 10, runDashboardApp);
                                }
                                else if (license.Status.Equals("In-Active"))
                                {
                                    PushNotification.PushNotification.ShowNotification("PC Screen Monitoring System", "Your 'Trial' license has become in-active. Now, you will be able to activate new license.", 10, runDashboardApp);
                                }
                            }
                        }
                    })));
                }
                catch (Exception)
                {
                    _working = false;
                }
            }
        }
        enum CalledBy
        {
            OnHubConnected,
            OnLicenseChanged,
            OnScheduleChanged
        }

        private void runDashboardApp(object sender, EventArgs e)
        {
            try
            {
                //isFound = false;
                //foreach (Process theprocess in Process.GetProcesses())
                //{
                //    if (theprocess.ProcessName.Equals("sa_vt"))
                //    {
                //        isFound = true;
                //    }
                //}

                //if (!isFound)
                //{
                //    // launch the application
                //    AppLauncher.PROCESS_INFORMATION procInfo;
                //    AppLauncher.StartProcessAndBypassUAC("sa_vt.exe", out procInfo);
                //}
            }
            catch (Exception)
            {
            }
        }
    }
}
