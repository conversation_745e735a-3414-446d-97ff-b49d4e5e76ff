﻿using System;
using System.Diagnostics;
using System.ServiceProcess;
using System.Timers;

namespace ScreenshotService
{
    public partial class CapturingService : ServiceBase
    {
        Timer timer;
        bool isFound = false;
        public CapturingService()
        {
            InitializeComponent();
        }

        public void OnDebug()
        {
            this.OnStart(null);
        }

        protected override void OnStart(string[] args)
        {
            LaunchApp();
            timer = new Timer();
            timer.Interval = 2 * 60 * 1000;
            timer.Enabled = true;
            timer.Elapsed += Timer_Elapsed;
            timer.Start();
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            LaunchApp();
        }

        private void LaunchApp()
        {
            try
            {
                isFound = false;
                foreach (Process theprocess in Process.GetProcesses())
                {
                    if (theprocess.ProcessName.Equals("sa_vt"))
                    {
                        isFound = true;
                    }
                }

                if (!isFound)
                {
                    // launch the application
                    AppLauncher.PROCESS_INFORMATION procInfo;
                    AppLauncher.StartProcessAndBypassUAC("sa_vt.exe", out procInfo);
                }
            }
            catch (Exception) { }
        }

        protected override void OnStop()
        {

        }

    }
}
