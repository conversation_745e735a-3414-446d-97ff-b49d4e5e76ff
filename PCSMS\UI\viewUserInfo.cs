﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using LocalDataBank;
using Gateway;
using Utils;

namespace PCSMS.UI
{
    public partial class viewUserInfo : UserControl
    {

        bool isWaiting = false;
        public event EventHandler OnSignOutSuccess;
        public event EventHandler OnChangePasswordClicked;
        public event EventHandler OnUserDetailsClicked;
        public viewUserInfo()
        {
            InitializeComponent();
        }

        
        public void BringToFront()
        {
            base.BringToFront();
            var user =SettingsDB.GetUserInfo();

            if(user != null)
            {
                lblCompany.Text = user.CompanyName;
                lblFullName.Text = user.FirstName + " "+ user.LastName;
                lblDesignation.Text = user.Designation;
                lblEmail.Text = user.Email;
            }
            else
            {
                lblCompany.Text = "N/A";
                lblDesignation.Text = "N/A";
                lblEmail.Text = "N/A";
            }
        }
        
        private void btnSignOut_Click(object sender, EventArgs e)
        {
           if(!isWaiting)
            {
                if (MessageBox.Show("Do you really want to sign out?", "Warning..", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    pbLoading.Visible = true;
                    isWaiting = true;

                    var server = new Server();
                    server.UserLogout(SettingsDB.GetUserInfo().Id, App.GetCpuId(), SettingsDB.GetCompany().Code, result => {
                        Invoke(new Action(() => {
                            pbLoading.Visible = false;
                            isWaiting = false;
                            if (result.IsSuccess)
                            {
                                SettingsDB.RemoveUserInfo();
                                OnSignOutSuccess?.Invoke(this, null);
                            }
                            else
                            {
                                MessageBox.Show(result.ErrorMessage, "Warning..");
                            }
                        }));
                    });
                }
            }
        }
        private void btnChangePassword_Click(object sender, EventArgs e)
        {
            OnChangePasswordClicked?.Invoke(this, null);
        }

        private void btnUserDetails_Click(object sender, EventArgs e)
        {
            OnUserDetailsClicked?.Invoke(this, null);
        }
    }
}
