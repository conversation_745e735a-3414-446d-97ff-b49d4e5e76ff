﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ToastNotifications;

namespace PushNotification
{
    public class PushNotification
    {
        public static void ShowNotification(string title, string body,int duration=5,EventHandler onClick=null)
        {
            var animationMethod = FormAnimator.AnimationMethod.Slide;
            var animationDirection = FormAnimator.AnimationDirection.Up;
            var toastNotification = new Notification(title, body, duration, animationMethod, animationDirection);
            toastNotification.OnClick += onClick;
            PlayNotificationSound();
            toastNotification.Show();
        }

        private static void PlayNotificationSound()
        {
            Stream str = Properties.Resources.normal;
            using (var player = new System.Media.SoundPlayer(str))
            {
                player.Play();
            }
        }
    }
}
