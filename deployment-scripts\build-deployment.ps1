# PCSMS Deployment Builder PowerShell Script
# This script automates the process of building and packaging the PCSMS application for deployment

param(
    [string]$SolutionPath = ".\PCSMS.sln",
    [string]$OutputPath = ".\PCSMS-Deployment",
    [string]$Configuration = "Release",
    [string]$Version = "1.0.0"
)

Write-Host "===============================================" -ForegroundColor Green
Write-Host "PCSMS Deployment Builder v1.0" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Check if Visual Studio Build Tools or MSBuild is available
$msbuildPath = ""
$vsPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

foreach ($path in $vsPaths) {
    if (Test-Path $path) {
        $msbuildPath = $path
        break
    }
}

if (-not $msbuildPath) {
    Write-Host "ERROR: MSBuild not found. Please install Visual Studio 2019/2022 or Build Tools." -ForegroundColor Red
    exit 1
}

Write-Host "Using MSBuild: $msbuildPath" -ForegroundColor Yellow
Write-Host ""

# Check if solution file exists
if (-not (Test-Path $SolutionPath)) {
    Write-Host "ERROR: Solution file not found: $SolutionPath" -ForegroundColor Red
    exit 1
}

# Clean previous build
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
& $msbuildPath $SolutionPath /p:Configuration=$Configuration /t:Clean /v:minimal
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Clean failed" -ForegroundColor Red
    exit 1
}

# Build solution
Write-Host "Building solution in $Configuration mode..." -ForegroundColor Yellow
& $msbuildPath $SolutionPath /p:Configuration=$Configuration /t:Build /v:minimal
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host ""

# Create deployment directory structure
Write-Host "Creating deployment package..." -ForegroundColor Yellow

if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}

$deploymentStructure = @(
    "$OutputPath\App",
    "$OutputPath\Install",
    "$OutputPath\Documentation",
    "$OutputPath\Support"
)

foreach ($dir in $deploymentStructure) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

# Define project output paths
$projects = @{
    "MainApp" = @{
        "Source" = "PCSMS\bin\$Configuration"
        "Output" = "Sm_system.exe"
        "Required" = $true
    }
    "ScreenCapture" = @{
        "Source" = "ScreenCapturingApp\bin\$Configuration"
        "Output" = "ScreenCapturingApp.exe"
        "Required" = $true
    }
    "Service" = @{
        "Source" = "ScreenshotService\bin\$Configuration"
        "Output" = "ScreenshotService.exe"
        "Required" = $true
    }
    "AppReset" = @{
        "Source" = "AppReset\bin\$Configuration"
        "Output" = "AppReset.exe"
        "Required" = $false
    }
}

# Copy application files
Write-Host "Copying application files..." -ForegroundColor Yellow

foreach ($project in $projects.Keys) {
    $sourceDir = $projects[$project]["Source"]
    $outputFile = $projects[$project]["Output"]
    $required = $projects[$project]["Required"]
    
    if (Test-Path $sourceDir) {
        Write-Host "  Copying $project files..." -ForegroundColor Gray
        
        # Copy all files from source directory
        Copy-Item "$sourceDir\*" "$OutputPath\App\" -Recurse -Force
        
        # Verify main executable exists
        if ($required -and -not (Test-Path "$OutputPath\App\$outputFile")) {
            Write-Host "ERROR: Required file not found: $outputFile" -ForegroundColor Red
            exit 1
        }
    } else {
        if ($required) {
            Write-Host "ERROR: Required source directory not found: $sourceDir" -ForegroundColor Red
            exit 1
        } else {
            Write-Host "  WARNING: Optional source directory not found: $sourceDir" -ForegroundColor Yellow
        }
    }
}

# Copy installation scripts
Write-Host "Copying installation scripts..." -ForegroundColor Yellow
$scriptFiles = @("setup.bat", "uninstall.bat")
foreach ($script in $scriptFiles) {
    if (Test-Path "deployment-scripts\$script") {
        Copy-Item "deployment-scripts\$script" "$OutputPath\Install\" -Force
    } else {
        Write-Host "  WARNING: Installation script not found: $script" -ForegroundColor Yellow
    }
}

# Copy documentation
Write-Host "Copying documentation..." -ForegroundColor Yellow
$docFiles = @("README.md", "DEPLOYMENT_GUIDE.md", "DEBUG_SCREEN_CAPTURE.md")
foreach ($doc in $docFiles) {
    if (Test-Path $doc) {
        Copy-Item $doc "$OutputPath\Documentation\" -Force
    }
}

# Create version info file
Write-Host "Creating version information..." -ForegroundColor Yellow
$versionInfo = @"
PCSMS Desktop Application
Version: $Version
Build Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Configuration: $Configuration
Built by: $env:USERNAME
Computer: $env:COMPUTERNAME

Components:
- Main Application (Sm_system.exe)
- Screen Capture App (ScreenCapturingApp.exe)
- Windows Service (ScreenshotService.exe)
- App Reset Utility (AppReset.exe)

Installation:
1. Run Install\setup.bat as Administrator
2. Follow the installation prompts
3. Start PCSMS from desktop shortcut

Support:
- Documentation: See Documentation folder
- Troubleshooting: See DEBUG_SCREEN_CAPTURE.md
- Contact: Your system administrator
"@

$versionInfo | Out-File "$OutputPath\VERSION.txt" -Encoding UTF8

# Create main README for deployment package
$deploymentReadme = @"
PCSMS Desktop Application - Deployment Package
==============================================

This package contains the complete PCSMS desktop application ready for installation.

CONTENTS:
- App\           : Application files and dependencies
- Install\       : Installation and uninstallation scripts
- Documentation\ : User guides and technical documentation
- Support\       : Diagnostic and support tools
- VERSION.txt    : Version and build information

INSTALLATION:
1. Extract this package to a temporary folder
2. Right-click on Install\setup.bat
3. Select "Run as administrator"
4. Follow the installation prompts

SYSTEM REQUIREMENTS:
- Windows 7/8/10/11 (64-bit recommended)
- .NET Framework 4.5.1 or higher
- Administrator privileges for installation
- Internet connection for server communication

SUPPORT:
For technical support, please contact your system administrator
or refer to the documentation in the Documentation folder.

Build Information:
- Version: $Version
- Build Date: $(Get-Date -Format "yyyy-MM-dd")
- Configuration: $Configuration
"@

$deploymentReadme | Out-File "$OutputPath\README.txt" -Encoding UTF8

# Create deployment summary
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "Deployment package created successfully!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "Package Location: $OutputPath" -ForegroundColor Yellow
Write-Host "Package Size: $((Get-ChildItem $OutputPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB) MB" -ForegroundColor Yellow
Write-Host ""
Write-Host "Contents:" -ForegroundColor White
Write-Host "  App Files: $(Get-ChildItem "$OutputPath\App" | Measure-Object | Select-Object -ExpandProperty Count) files" -ForegroundColor Gray
Write-Host "  Documentation: $(Get-ChildItem "$OutputPath\Documentation" | Measure-Object | Select-Object -ExpandProperty Count) files" -ForegroundColor Gray
Write-Host "  Installation Scripts: $(Get-ChildItem "$OutputPath\Install" | Measure-Object | Select-Object -ExpandProperty Count) files" -ForegroundColor Gray
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor White
Write-Host "1. Test the installation on a clean system" -ForegroundColor Gray
Write-Host "2. Create a ZIP archive for distribution" -ForegroundColor Gray
Write-Host "3. Provide the package to your clients" -ForegroundColor Gray
Write-Host ""

# Optional: Create ZIP archive
$createZip = Read-Host "Create ZIP archive for distribution? (Y/N)"
if ($createZip -eq "Y" -or $createZip -eq "y") {
    $zipPath = "$OutputPath-v$Version.zip"
    Write-Host "Creating ZIP archive: $zipPath" -ForegroundColor Yellow
    
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
    }
    
    Compress-Archive -Path "$OutputPath\*" -DestinationPath $zipPath -CompressionLevel Optimal
    Write-Host "ZIP archive created successfully!" -ForegroundColor Green
    Write-Host "Archive Size: $((Get-Item $zipPath).Length / 1MB) MB" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Deployment build completed!" -ForegroundColor Green
