# Screen Capture Debugging Guide

## Overview

If screen capture is not working after login, there are several validation checks that must pass before screenshots are taken. This guide will help you identify and resolve the issues.

## Validation Chain

The `AllRequiredValidationPassed()` method in `ScreenshotHandler.cs` performs these checks:

1. **License Status** - Must be "Active" and not expired
2. **User Login** - User must be logged in
3. **Work Schedule** - Must be configured
4. **Working Day** - Current day must be a working day
5. **Working Hours** - Current time must be within working hours

## Debugging Steps

### Step 1: Check License Status

```csharp
// Add this debug code to check license
var license = SettingsDB.GetLicense();
if (license == null)
{
    Console.WriteLine("ERROR: No license found");
}
else
{
    Console.WriteLine($"License Status: {license.Status}");
    Console.WriteLine($"Expiry Date: {license.ExpiryDate}");
    Console.WriteLine($"Current Date: {DateTime.Now}");
    Console.WriteLine($"Is Active: {license.Status.Equals("Active")}");
    Console.WriteLine($"Is Not Expired: {DateTime.Now <= license.ExpiryDate}");
}
```

### Step 2: Check User Login Status

```csharp
// Add this debug code to check user login
var user = SettingsDB.GetUserInfo();
if (user == null)
{
    Console.WriteLine("ERROR: No user logged in");
}
else
{
    Console.WriteLine($"User ID: {user.Id}");
    Console.WriteLine($"User Email: {user.Email}");
    Console.WriteLine($"User Name: {user.FirstName} {user.LastName}");
}
```

### Step 3: Check Work Schedule

```csharp
// Add this debug code to check work schedule
var schedule = SettingsDB.GetWorkSchedule();
if (schedule == null)
{
    Console.WriteLine("ERROR: No work schedule found");
}
else
{
    Console.WriteLine($"Start Time: {schedule.StartTime}");
    Console.WriteLine($"End Time: {schedule.EndTime}");
    Console.WriteLine($"Interval: {schedule.IntervalInMinute} minutes");
    Console.WriteLine($"Is Random: {schedule.IsRandom}");
    
    // Check working days
    Console.WriteLine("Working Days:");
    foreach (var day in schedule.WorkingDays)
    {
        Console.WriteLine($"  {day.Key}: {day.Value}");
    }
    
    // Check current day
    string currentDay = DateTime.Now.DayOfWeek.ToString();
    bool isWorkingDay = schedule.WorkingDays.ContainsKey(currentDay) && schedule.WorkingDays[currentDay];
    Console.WriteLine($"Current Day: {currentDay}");
    Console.WriteLine($"Is Working Day: {isWorkingDay}");
}
```

### Step 4: Check Working Hours

```csharp
// Add this debug code to check working hours
var schedule = SettingsDB.GetWorkSchedule();
if (schedule != null)
{
    var startTime = DateTime.Parse(schedule.StartTime.ToString("HH:mm:ss"));
    var endTime = DateTime.Parse(schedule.EndTime.ToString("HH:mm:ss"));
    var currentTime = DateTime.Now;
    
    Console.WriteLine($"Start Time: {startTime.ToString("HH:mm:ss")}");
    Console.WriteLine($"End Time: {endTime.ToString("HH:mm:ss")}");
    Console.WriteLine($"Current Time: {currentTime.ToString("HH:mm:ss")}");
    Console.WriteLine($"Is Within Hours: {startTime <= currentTime && endTime >= currentTime}");
}
```

## Common Issues & Solutions

### Issue 1: License Not Active

**Symptoms:**
- License status is not "Active"
- License has expired

**Solutions:**
1. Check license activation in the main app
2. Reactivate license if expired
3. Contact administrator for new license

### Issue 2: No Work Schedule

**Symptoms:**
- Work schedule is null
- No working days configured

**Solutions:**
1. Login to main app and check dashboard
2. Ensure work schedule is downloaded from server
3. Check server-side device configuration

### Issue 3: Outside Working Hours

**Symptoms:**
- Current time is outside configured working hours
- Current day is not a working day

**Solutions:**
1. Adjust work schedule on server
2. Test during configured working hours
3. Enable 24/7 monitoring if needed

### Issue 4: Screenshot Capture Fails

**Symptoms:**
- Validation passes but no screenshots generated
- Bitmap is null

**Solutions:**
1. Check screen resolution and display settings
2. Run application as administrator
3. Check for graphics driver issues
4. Test on different display configurations

## Debug Code Integration

### Add Debug Logging to ScreenshotHandler

```csharp
private void ScreencaptureTimer_Elapsed(object sender, ElapsedEventArgs e)
{
    try
    {
        isRandomIntervalElapsed = true;
        Console.WriteLine($"Screenshot timer elapsed at {DateTime.Now}");
        
        if (AllRequiredValidationPassed())
        {
            Console.WriteLine("Validation passed, capturing screenshot...");
            var img = GenerateScreenshotImage(screenshot.FullScreenshot());
            if(img != null)
            {
                Console.WriteLine("Screenshot captured successfully");
                screenshotDB.storeScreenshot(img);
                Console.WriteLine("Screenshot stored in local database");
            }
            else
            {
                Console.WriteLine("ERROR: Failed to generate screenshot image");
            }
            img = null;
        }
        else
        {
            Console.WriteLine("Validation failed, skipping screenshot");
            // Add detailed validation debug here
            DebugValidation();
        }
    }
    catch (Exception ex) 
    { 
        Console.WriteLine($"Exception in screenshot capture: {ex.Message}");
    }
}

private void DebugValidation()
{
    var license = SettingsDB.GetLicense();
    var user = SettingsDB.GetUserInfo();
    var schedule = SettingsDB.GetWorkSchedule();
    
    Console.WriteLine("=== VALIDATION DEBUG ===");
    Console.WriteLine($"License: {(license != null ? "Found" : "NULL")}");
    if (license != null)
    {
        Console.WriteLine($"  Status: {license.Status}");
        Console.WriteLine($"  Expires: {license.ExpiryDate}");
        Console.WriteLine($"  Is Active: {license.Status.Equals("Active")}");
        Console.WriteLine($"  Not Expired: {DateTime.Now <= license.ExpiryDate}");
    }
    
    Console.WriteLine($"User: {(user != null ? "Logged In" : "NULL")}");
    
    Console.WriteLine($"Schedule: {(schedule != null ? "Found" : "NULL")}");
    if (schedule != null)
    {
        string currentDay = DateTime.Now.DayOfWeek.ToString();
        bool isWorkingDay = schedule.WorkingDays.ContainsKey(currentDay) && schedule.WorkingDays[currentDay];
        
        Console.WriteLine($"  Current Day: {currentDay}");
        Console.WriteLine($"  Is Working Day: {isWorkingDay}");
        
        if (isWorkingDay)
        {
            var startTime = DateTime.Parse(schedule.StartTime.ToString("HH:mm:ss"));
            var endTime = DateTime.Parse(schedule.EndTime.ToString("HH:mm:ss"));
            var currentTime = DateTime.Now;
            
            Console.WriteLine($"  Start: {startTime.ToString("HH:mm:ss")}");
            Console.WriteLine($"  End: {endTime.ToString("HH:mm:ss")}");
            Console.WriteLine($"  Current: {currentTime.ToString("HH:mm:ss")}");
            Console.WriteLine($"  In Hours: {startTime <= currentTime && endTime >= currentTime}");
        }
    }
    Console.WriteLine("========================");
}
```

## Testing Steps

### 1. Manual Testing
1. Build and run the ScreenCapturingApp project
2. Add debug console output to see validation results
3. Check local database for stored screenshots
4. Monitor network traffic for upload attempts

### 2. Service Testing
1. Install and start the ScreenshotService
2. Check Windows Event Viewer for service logs
3. Monitor process list for `sa_vt.exe` process
4. Verify service is launching capture app correctly

### 3. Database Testing
1. Check LiteDB files in `%AppData%` folder
2. Verify license, user, and schedule data
3. Check screenshot storage and retrieval
4. Test database cleanup after uploads

## Quick Fixes

### Force Screenshot for Testing
```csharp
// Bypass validation for testing
private void TestScreenshot()
{
    var img = GenerateScreenshotImage(screenshot.FullScreenshot());
    if(img != null)
    {
        screenshotDB.storeScreenshot(img);
        Console.WriteLine("Test screenshot saved");
    }
}
```

### Reset Work Schedule
```csharp
// Create default 24/7 schedule for testing
var testSchedule = new WorkSchedule
{
    Id = 1,
    StartTime = DateTime.Today,
    EndTime = DateTime.Today.AddHours(23).AddMinutes(59),
    IntervalInMinute = 1,
    IsRandom = "N",
    WorkingDays = new Dictionary<string, bool>
    {
        {"Monday", true},
        {"Tuesday", true},
        {"Wednesday", true},
        {"Thursday", true},
        {"Friday", true},
        {"Saturday", true},
        {"Sunday", true}
    }
};
SettingsDB.UpdateWorkingSchedule(testSchedule);
```

## Next Steps

1. Add the debug code to your ScreenshotHandler
2. Run the application and check console output
3. Identify which validation is failing
4. Apply appropriate fix based on the issue
5. Test screenshot capture and upload functionality

The most common issue is usually missing or incorrect work schedule configuration from the server.
