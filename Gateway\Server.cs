﻿using ObjectModels;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using RestSharp;
namespace Gateway
{
    public class Server
    {
      

        //public static string BaseUrl = "http://testpcsms.akhtar3rdeye.com/";
        //public static string BaseUrl = "http://pcsms.bacbonltd.net/";
        public static string BaseUrl = "http://localhost:1839/";
        RestClient client = new RestClient(BaseUrl);
        public delegate void OnRequestComplete<T>(Result<T> result);

   
      

        public void IsCompanyCodeValid(string companyCode,OnRequestComplete<bool> callback)
        {
            var result =new Result<bool>();
            try
            {
                  client.ExecuteAsync<bool>(new RestRequest("api/Device/IsCompanyCodeValid/" + companyCode), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        result.IsSuccess = true;
                        result.Content = response.Data;

                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                          callback(result);
                      }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void ActivateLicense(string licenseKey, string deviceName, string uId, string companyCode, OnRequestComplete<License> callback)
        {
            var result = new Result<License>();
            try
            {
                string url = String.IsNullOrEmpty(licenseKey)?
                    "api/Device/ActivateFreeTrial/" + deviceName+"/"+uId+"/"+companyCode :
                    "api/Device/ActivateLicenseKey/" + deviceName + "/" + uId + "/" + companyCode+"/"+licenseKey;
               


                client.ExecuteAsync<ServerResult<License>>(new RestRequest(url,Method.POST), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if(response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }

      

        public void UserLogin(string email, string password, string uId, string companyCode, OnRequestComplete<LoginResult> callback)
        {
            var result = new Result<LoginResult>();
            try
            {
                string url = $"api/Device/LogIn/{email}/{password}/{companyCode}/{uId}";
             
                client.ExecuteAsync<ServerResult<LoginResult>>(new RestRequest(url,Method.POST), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if(response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void UserLogout(int employeeId, string uId, string companyCode, OnRequestComplete<string> callback)
        {
            var result = new Result<string>();
            try
            {
                string url = $"api/Device/Logout/{employeeId}";
             
                client.ExecuteAsync<ServerResult<string>>(new RestRequest(url,Method.POST), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if(response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void ChangePassword(int employeeId, string oldPass, string newPass, string uId, string companyCode, OnRequestComplete<string> callback)
        {
            var result = new Result<string>();
            try
            {
                string url = $"api/Device/ChangeCP_UserPassword/{employeeId}/{oldPass}/{newPass}";

                client.ExecuteAsync<ServerResult<string>>(new RestRequest(url, Method.POST), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void ResetPassword(string email, string uId, string companyCode, OnRequestComplete<string> callback)
        {
            var result = new Result<string>();
            try
            {
                string url = $"api/Device/ForgotPassword/{email}/{companyCode}";

                client.ExecuteAsync<ServerResult<string>>(new RestRequest(url, Method.POST), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void UploadScreenshots(List<ScreenshotImage> screenshots, OnRequestComplete<string> callback)
        {
            var result = new Result<string>();
            try
            {
                string url = $"api/Device/SaveScreenCapture";
                var request = new RestRequest(url, Method.POST);
                request.AddHeader("Content-Type", "application/json");
                request.AddJsonBody(screenshots);


                client.ExecuteAsync<ServerResult<string>>(request, response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }



        public void UpdateUserInfo(UserInfo user, OnRequestComplete<UserInfo> callback)
        {
            
            var result = new Result<UserInfo>();
            try
            {
                user.ProfilePic = null;
                string url = "api/Device/UpdateCP_UserProfile";
                var request = new RestRequest(url, Method.POST);
                request.AddHeader("Content-Type", "application/json");
                var jObject = new UploadObject();
                jObject.UserObj = user;
                request.AddJsonBody(jObject);


                client.ExecuteAsync<ServerResult<UserInfo>>(request, response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void UploadImage(string imagePath,int employeeId, OnRequestComplete<string> callback)
        {
         
            var result = new Result<string>();
            try
            {
                string url = "api/Device/UploadCP_UserPhoto/" + employeeId.ToString();
                var request = new RestRequest(url, Method.POST);
                request.AddHeader("accept", "application/json");
                request.AddHeader("Content-Type", "multipart/form-data");
                request.AddFile("content", imagePath);

                
                client.ExecuteAsync<ServerResult<string>>(request, response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.Data.IsReport.Equals("Ok"))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
        public void DownloadImage(string imagePath, OnRequestComplete<Byte[]> callback)
        {

            var result = new Result<Byte[]>();
            try
            {
                string url = "Company_Images/User_Images/" + imagePath;
                var request = new RestRequest(url);


                client.ExecuteAsync(request, response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (response.StatusCode == System.Net.HttpStatusCode.NotAcceptable && response.RawBytes.Length >0)
                        {

                            result.IsSuccess = true;
                            result.SuccessMessage ="Download success";
                            result.Content = response.RawBytes;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = "Download failed";
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        // callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                //callback(result);
            }
        }

        public void GetDeviceAndLicenseDetails(int deviceId, OnRequestComplete<LoginResult> callback)
        {
            var result = new Result<LoginResult>();
            try
            {
                string url = $"api/Device/GetDeviceAndLicenseDetails/{deviceId}";

                client.ExecuteAsync<ServerResult<LoginResult>>(new RestRequest(url, Method.GET), response => {
                    if (response.ResponseStatus == ResponseStatus.Completed)
                    {
                        if (string.IsNullOrEmpty(response.Data.Message))
                        {
                            result.IsSuccess = true;
                            result.SuccessMessage = response.Data.Message;
                            result.Content = response.Data.Content;
                        }
                        else
                        {
                            result.IsSuccess = false;
                            result.ErrorMessage = response.Data.Message;
                        }
                        callback(result);
                    }
                    else
                    {
                        result.IsSuccess = false;
                        result.ErrorMessage = response.ErrorMessage;

                        if (response.StatusCode == 0)
                        {
                            result.ErrorMessage = "Internet connection problem!";
                        }
                        callback(result);
                    }
                });
            }
            catch (Exception)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "Someting wrong happend!";
                callback(result);
            }
        }
    }
}
