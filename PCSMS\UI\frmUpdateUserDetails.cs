﻿using Gateway;
using LocalDataBank;
using ObjectModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PCSMS.UI
{
    public partial class frmUpdateUserDetails : Form
    {
        UserInfo user;
        public event EventHandler OnUserDetailsUpdated;
        bool isWaiting;


        #region DropShadow
        //===================================*******============================

        private const int WM_NCHITTEST = 0x84;
        private const int HTCLIENT = 0x1;
        private const int HTCAPTION = 0x2;

        private bool m_aeroEnabled;

        private const int CS_DROPSHADOW = 0x00020000;
        private const int WM_NCPAINT = 0x0085;
        private const int WM_ACTIVATEAPP = 0x001C;

        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmExtendFrameIntoClientArea(IntPtr hWnd, ref MARGINS pMarInset);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]
        public static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);
        [System.Runtime.InteropServices.DllImport("dwmapi.dll")]

        public static extern int DwmIsCompositionEnabled(ref int pfEnabled);
        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(
            int nLeftRect,
            int nTopRect,
            int nRightRect,
            int nBottomRect,
            int nWidthEllipse,
            int nHeightEllipse
            );

        public struct MARGINS
        {
            public int leftWidth;
            public int rightWidth;
            public int topHeight;
            public int bottomHeight;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                m_aeroEnabled = CheckAeroEnabled();
                CreateParams cp = base.CreateParams;
                if (!m_aeroEnabled)
                    cp.ClassStyle |= CS_DROPSHADOW; return cp;
            }
        }
        private bool CheckAeroEnabled()
        {
            if (Environment.OSVersion.Version.Major >= 6)
            {
                int enabled = 0; DwmIsCompositionEnabled(ref enabled);
                return (enabled == 1) ? true : false;
            }
            return false;
        }
        protected override void WndProc(ref Message m)
        {
            switch (m.Msg)
            {
                case WM_NCPAINT:
                    if (m_aeroEnabled)
                    {
                        var v = 2;
                        DwmSetWindowAttribute(this.Handle, 2, ref v, 4);
                        MARGINS margins = new MARGINS()
                        {
                            bottomHeight = 1,
                            leftWidth = 0,
                            rightWidth = 0,
                            topHeight = 0
                        }; DwmExtendFrameIntoClientArea(this.Handle, ref margins);
                    }
                    break;
                default: break;
            }
            if (m.Msg == WM_NCHITTEST && (int)m.Result == HTCLIENT) m.Result = (IntPtr)HTCAPTION;

            base.WndProc(ref m);
        }

        //===================================*******============================
        #endregion
        public frmUpdateUserDetails()
        {
            InitializeComponent();
            resetErrorText();
            user = SettingsDB.GetUserInfo();
            if (user != null)
            {
                mtbFirstName.MText = user.FirstName;
                mtbLastName.MText = user.LastName;
                if(!string.IsNullOrEmpty(user.Gender))
                {
                    setGender(user.Gender);
                }
                if (!string.IsNullOrEmpty(user.BirthDate))
                {
                    try
                    {
                        var date = DateTime.ParseExact(user.BirthDate, "dd/MM/yyyy", CultureInfo.InvariantCulture);
                        dtBirthday.Text = date.ToString("MM/dd/yyyy");
                    }
                    catch (Exception) { }
                }
                mtbMobile.MText = user.Mobile;
                mtbAddress.MText = user.Address;
            }

            dtBirthday.MaxDate = DateTime.Now;
        }

        private void setGender(string gender)
        {
            try
            {
                if (gender.Equals("Male"))
                {
                    cbMale.Checked = true;
                    cbFemale.Checked = false;
                }
                else
                {
                    cbMale.Checked = false;
                    cbFemale.Checked = true;
                }
            }
            catch (Exception) { }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            ControlPaint.DrawBorder(e.Graphics, ClientRectangle, Color.Gray, ButtonBorderStyle.Solid);
        }
        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }
        private void btnCancel_Click(object sender, EventArgs e)
        {
            Close();
        }


        private void cbMale_OnChange(object sender, EventArgs e)
        {
            cbFemale.Checked = !cbMale.Checked;
        }
        private void cbFemale_OnChange(object sender, EventArgs e)
        {
            cbMale.Checked = !cbFemale.Checked;
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            resetErrorText();
            if(!isWaiting)
            {
                if(string.IsNullOrEmpty(mtbFirstName.MText))
                {
                    mtbFirstName.Focus();
                    erFirstname.Text = "First name is required!";
                }else if(string.IsNullOrEmpty(mtbLastName.MText))
                {
                    mtbLastName.Focus();
                    erLastname.Text = "Last name is required!";
                }
                else if (!string.IsNullOrEmpty(mtbMobile.MText) && ( mtbMobile.MText.Count() < 10 || mtbMobile.MText.Count() > 20) )
                {
                    erMobile.Text = "Invalid mobile number length!";
                }
                else
                {
                    GenerateUserObject();
                    DoUserInfoUpdate();
                }
            }
        }
        private void DoUserInfoUpdate()
        {
            pbLoading.Visible = true;
            isWaiting = true;

            try
            {
                var server = new Server();
                server.UpdateUserInfo(user,result=> {
                    Invoke(new Action(() => {
                        pbLoading.Visible = false;
                        isWaiting = false;
                        if (result.IsSuccess)
                        {
                            SettingsDB.StoreUserInfo(result.Content);
                            OnUserDetailsUpdated?.Invoke(this, null);
                            MessageBox.Show(result.SuccessMessage, "Success..");
                            Close();
                        }
                        else
                        {
                            MessageBox.Show(result.ErrorMessage, "Warning..");
                        }
                    }));
                });
            }
            catch (Exception)
            {
                pbLoading.Visible = false;
                isWaiting = false;
            }

        }


        private void GenerateUserObject()
        {
            user.FirstName = mtbFirstName.MText;
            user.LastName = mtbLastName.MText;
            user.BirthDate = dtBirthday.Value.ToString("dd/MM/yyyy");
            user.Mobile = mtbMobile.MText;
            user.Address = mtbAddress.MText;

            if(cbMale.Checked)
            {
                user.Gender = "Male";
            }else
            {
                user.Gender = "Female";
            }
        }

        private void resetErrorText()
        {
            erFirstname.Text = null;
            erLastname.Text = null;
            erMobile.Text = null;
        }

        private void btnMinimize_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }
    }
}
